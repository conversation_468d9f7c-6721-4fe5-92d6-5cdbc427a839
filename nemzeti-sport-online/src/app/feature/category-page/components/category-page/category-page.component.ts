import { Async<PERSON>ipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LayoutApiData,
  LayoutPageType,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
} from '@trendency/kesma-ui';
import { map, Observable, Subject, takeUntil } from 'rxjs';
import { RadioService } from 'src/app/shared/services/radio.service';
import {
  addCategoryAds,
  AdvertService,
  categoriesMetaInfo,
  CategoryArticleListComponent,
  ColorChangeService,
  createNSOTitle,
  defaultMetaInfo,
  NsoPagerComponent,
} from '../../../../shared';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { CsupasportTagsComponent } from '../csupasport-tags/csupasport-tags.component';

@Component({
  selector: 'app-category-page',
  templateUrl: './category-page.component.html',
  styleUrls: ['./category-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, LayoutComponent, CategoryArticleListComponent, CsupasportTagsComponent, NsoPagerComponent, SidebarComponent],
})
export class CategoryPageComponent implements OnInit, OnDestroy {
  public LayoutPageType = LayoutPageType;

  hasLayout: boolean;
  private readonly colorChangeService = inject(ColorChangeService);
  readonly isCsupasport = computed(() => this.colorChangeService.isCsupasport());
  readonly isHatsofuves = computed(() => this.colorChangeService.isHatsofuves());

  public pageData$: Observable<{
    columnTitle: string;
    columnSlug: string;
    layoutData: LayoutApiData;
  }> = this.route.data.pipe(
    map((res: any) => {
      const columnSlug: string = res['pageData']['slug'];

      this.disableAdsOnHatsoFuves(columnSlug);

      this.setMetaData(res['pageData']['columnTitle'], res['pageData']['slug']);
      this.analyticsService.sendPageView({
        pageCategory: columnSlug,
      });

      const layoutWithAds = res['pageData']['layoutApiResponse'];

      this.hasLayout = layoutWithAds?.struct[0]?.elements[1]?.elements?.length > 0;
      if (columnSlug === 'csupasport') {
        this.colorChangeService.setIsCsupasport(true);
        this.colorChangeService.setIsHatsoFuves(false);
        this.colorChangeService.setRedToYellow('#FFE035');
        this.colorChangeService.setWhiteToGray('#646464');
        this.colorChangeService.setWhiteToBlack('#000000');
        this.colorChangeService.setWhiteToTurquoise('#1BA1C7');
        this.colorChangeService.setWhiteToDarkYellow('#A69122');
        this.colorChangeService.setGrayToLightGray('#646464');
      } else if (columnSlug === 'hatsofuves') {
        this.colorChangeService.setIsHatsoFuves(true);
        this.colorChangeService.setIsCsupasport(false);
        this.colorChangeService.setRedToYellow('#CFC622');
        this.colorChangeService.setWhiteToGray('#000000');
        this.colorChangeService.setWhiteToBlack('#000000');
        this.colorChangeService.setWhiteToTurquoise('#FDFABC');
        this.colorChangeService.setWhiteToDarkYellow('#A69122');
        this.colorChangeService.setGrayToLightGray('#000000');
      } else {
        this.colorChangeService.setAllPropertiesDefault();
      }

      return {
        columnTitle: res['pageData']['columnTitle'],
        layoutData: addCategoryAds(layoutWithAds),
        columnSlug,
      };
    })
  );

  articles$: Observable<ArticleCard[]> = this.route.data.pipe(
    map(
      ({
        pageData: {
          category: { data },
        },
      }) =>
        data.map(mapBackendArticleDataToArticleCard).map((data: ArticleCard) => ({
          ...data,
          thumbnail: {
            ...data.thumbnail,
            alt: data.thumbnail?.alt,
          },
        }))
    )
  );

  limitables$: Observable<LimitableMeta> = this.route.data.pipe(
    map(
      ({
        pageData: {
          category: { meta },
        },
      }) => meta.limitable
    )
  );

  private readonly destroy$: Subject<boolean> = new Subject();

  constructor(
    public readonly radioService: RadioService,
    private readonly analyticsService: AnalyticsService,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly advertService: AdvertService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.colorChangeService.setAllPropertiesDefault();
    this.enableAds();
  }

  enableAds(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.advertService.enableAds();
    window.adsDoNotServeAds = false;
  }

  disableAdsOnHatsoFuves(columnSlug: string): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    // Hatso Fuves column has to be advertisement free as per KESMA-26305
    if (columnSlug === 'hatsofuves' || columnSlug === 'hatso-fuves') {
      this.advertService.disableAds();
      window.adsDoNotServeAds = true;
    }
  }

  private setMetaData(title: string, slug: string): void {
    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    // If category has a predefined metadata to be used.
    if (slug in categoriesMetaInfo) {
      const overrideMeta = categoriesMetaInfo[slug];
      const title = createNSOTitle(overrideMeta.title);
      const meta: IMetaData = {
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
        description: overrideMeta.description,
        ogDescription: overrideMeta.description,
        keywords: overrideMeta.keywords,
      };
      this.seo.setMetaData(meta);
    } else {
      //If not, use the default ones.
      title = createNSOTitle(title);
      const metaData: IMetaData = {
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
      };
      this.seo.setMetaData(metaData);
    }
  }
}
