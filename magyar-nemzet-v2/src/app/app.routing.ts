import { Route, Routes } from '@angular/router';
import { BaseComponent, Error404Component, InitResolver, VignetteAdService } from './shared';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const routes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.LAYOUT_EDITOR_ROUTES),
    resolve: { data: InitResolver },
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: InitResolver },
    children: (
      [
        {
          path: '',
          pathMatch: 'full',
          loadChildren: () => import('./feature/home/<USER>').then((m) => m.HOME_ROUTES),
        },
        // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
        {
          path: 'assets/:file',
          redirectTo: '404',
        },
        {
          path: 'assets/:dir/:file',
          redirectTo: '404',
        },
        {
          path: 'script/:file',
          redirectTo: '404',
        },
        {
          // Advertisements
          path: 'hirdetesek',
          loadChildren: () => import('./feature/advertisement/advertisement.routing').then((m) => m.ADVERTISEMENT_ROUTES),
        },
        {
          // Rovat
          path: 'rovat/:categorySlug',
          loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          path: 'galeriak',
          children: [
            {
              path: '',
              pathMatch: 'full',
              loadChildren: () => import('./feature/galleries/galleries.routing').then((m) => m.GALLERIES_ROUTES),
            },
          ],
          data: {
            header: 'dark',
          },
        },
        {
          path: 'galeria/:gallerySlug',
          loadChildren: () => import('./feature/gallery-detail/gallery-detail.routing').then((m) => m.GALLERY_DETAIL_ROUTES),
          data: {
            isFullWidth: true,
          },
        },
        {
          // Dossziék
          path: 'dossziek',
          pathMatch: 'full',
          loadChildren: () => import('./feature/dossier/dossier.routing').then((m) => m.DOSSIER_ROUTES),
          data: {
            isFullWidth: false,
          },
        },
        {
          // Dossziék slug
          path: 'dosszie/:slug',
          pathMatch: 'full',
          loadChildren: () => import('./feature/dossier-list/dossier-list.routing').then((m) => m.DOSSIER_LIST_ROUTES),
          data: {
            isFullWidth: false,
          },
        },
        {
          path: 'hirfolyam/:newsfeedSlug',
          pathMatch: 'full',
          loadChildren: () => import('./feature/news-feed/news-feed.routing').then((m) => m.NEWS_FEED_ROUTING),
          data: {
            isFullWidth: false,
          },
        },
        /*      {
        path: 'automotor',
        redirectTo: '/rovat/auto-motor',
      },
      {
        path: 'auto-motor',
        redirectTo: '/rovat/auto-motor',
      },
      {
        path: 'mindmegette',
        redirectTo: '/rovat/mindmegette',
      },
      {
        path: 'unilife',
        redirectTo: '/rovat/mindmegette',
      },
      {
        path: 'lakaskultura',
        redirectTo: '/rovat/lakaskultura',
      },*/
        // Branding lista
        {
          path: 'automotor',
          pathMatch: 'full',
          loadChildren: () => import('./feature/branding-list/branding-list.routing').then((m) => m.BRANDING_LIST_ROUTES),
          data: { routeType: 'automotor' },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'auto-motor',
          pathMatch: 'full',
          loadChildren: () => import('./feature/branding-list/branding-list.routing').then((m) => m.BRANDING_LIST_ROUTES),
          data: { routeType: 'auto-motor' },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'mindmegette',
          pathMatch: 'full',
          loadChildren: () => import('./feature/branding-list/branding-list.routing').then((m) => m.BRANDING_LIST_ROUTES),
          data: { routeType: 'mindmegette' },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'lakaskultura',
          pathMatch: 'full',
          loadChildren: () => import('./feature/branding-list/branding-list.routing').then((m) => m.BRANDING_LIST_ROUTES),
          data: { routeType: 'lakaskultura' },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'unilife',
          pathMatch: 'full',
          loadChildren: () => import('./feature/branding-list/branding-list.routing').then((m) => m.BRANDING_LIST_ROUTES),
          data: { routeType: 'unilife' },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'orszagszerte',
          pathMatch: 'full',
          loadChildren: () => import('./feature/branding-list/branding-list.routing').then((m) => m.BRANDING_LIST_ROUTES),
          data: { routeType: 'orszagszerte' },
          canActivate: [PageValidatorGuard],
        },

        // Branding tartalmak
        /*    {
        path: 'brand/:categorySlug/:year/:month/:articleSlug',
        loadChildren: () => import('./feature/article/article.routing').then((m) => m.ARTICLE_ROUTES),
        data: { routeType: 'automotor' },
      },*/
        {
          path: 'brand/:articleSlug',
          loadChildren: () => import('./feature/branding/branding.routing').then((m) => m.BRANDING_ROUTES),
        },
        {
          path: 'brand/auto-motor/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/branding/branding.routing').then((m) => m.BRANDING_ROUTES),
          data: { routeType: 'auto-motor' },
        },
        {
          path: 'brand/mindmegette/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/branding/branding.routing').then((m) => m.BRANDING_ROUTES),
          data: { routeType: 'mindmegette' },
        },
        {
          path: 'brand/lakaskultura/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/branding/branding.routing').then((m) => m.BRANDING_ROUTES),
          data: { routeType: 'lakaskultura' },
        },
        {
          path: 'brand/orszagszerte/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/branding/branding.routing').then((m) => m.BRANDING_ROUTES),
          data: { routeType: 'orszagszerte' },
        },
        {
          path: 'brand/unilife/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/branding/branding.routing').then((m) => m.BRANDING_ROUTES),
          data: { routeType: 'unilife' },
        },

        {
          path: 'video',
          loadChildren: () => import('./feature/video-podcast-list-page/video-podcast-list-page.routing').then((m) => m.VIDEO_POTCAST_LIST_PAGE_ROUTES),
          data: {
            isFullWidth: false,
            contentType: 'video',
          },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'podcast',
          loadChildren: () => import('./feature/video-podcast-list-page/video-podcast-list-page.routing').then((m) => m.VIDEO_POTCAST_LIST_PAGE_ROUTES),
          data: {
            isFullWidth: false,
            contentType: 'podcast',
          },
          canActivate: [PageValidatorGuard],
        },
        {
          path: 'videok',
          redirectTo: '/video',
        },
        {
          path: 'szerzo',
          loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHOR_ROUTES),
        },
        {
          path: 'szerzok',
          loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHOR_ROUTES),
        },
        {
          // Hírlevél
          path: 'hirlevel',
          loadChildren: () => import('./feature/newsletter/newsletter-routing').then((m) => m.NEWSLETTER_ROUTES),
        },

        {
          path: 'hirlevel-feliratkozas',
          redirectTo: 'hirlevel/feliratkozas',
        },
        {
          // Tag szűrő kereső (cimke?q=<search string>)
          path: 'cimke/:slug',
          loadChildren: () => import('./feature/tag-list/tag-list.routing').then((m) => m.TAG_LIST_ROUTES),
        },
        {
          // Kereses
          path: 'kereses',
          loadChildren: () => import('./feature/search-page/search-page.routing').then((m) => m.SEARCH_PAGE_ROUTES),
        },
        {
          path: 'kvizek',
          loadChildren: () => import('./feature/quiz/quiz.routing').then((m) => m.quizRouting),
        },
        {
          // Kultúrnemzet programok
          path: 'kulturnemzet',
          loadChildren: () => import('./feature/kulturnemzet/kulturnemzet.routing').then((m) => m.KULTURNEMZET_ROUTES),
          data: {
            isFullWidth: true,
          },
        },
        {
          path: 'file/:fileId/:fileName',
          loadChildren: () => import('./feature/file/file.routing').then((m) => m.FILE_ROUTES),
        },
        {
          path: 'elrendezes-elonezet/:layoutHash',
          loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
        },
        {
          path: '404',
          component: Error404Component,
        },
        {
          path: 'elrendezes-elonezet/:layoutHash',
          loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
        },
        {
          path: 'cikk-elonezet/:previewHash',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: {
            skipSeoMetaCheck: true,
            omitGlobalPageView: true,
          },
        },
        {
          path: 'cikk-elonezet/:previewHash/:previewType',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: {
            skipSeoMetaCheck: true,
            omitGlobalPageView: true,
          },
        },
        {
          path: 'sportlexikon',
          loadChildren: () => import('./feature/glossary/glossary.routing').then((m) => m.GLOSSARY_ROUTES),
        },
        {
          path: 'alapko-tartalom',
          loadChildren: () => import('./feature/foundation-content/foundation-content.routing').then((m) => m.FOUNDATION_CONTENT_ROUTING),
        },
        // Olimpia
        {
          path: 'olimpia-2024/menetrend',
          loadChildren: () => import('./feature/olympics-schedules/olympics-schedules.routing').then((m) => m.OLYMPICS_SCHEDULES_ROUTES),
        },
        {
          path: 'olimpia-2024/dicsosegtabla',
          loadChildren: () => import('./feature/olympics-national-medals/olympics-national-medals.routing').then((m) => m.OLYMPICS_NATIONAL_MEDALS_ROUTES),
        },
        {
          path: 'olimpia-2024/magyar-csapat',
          loadChildren: () => import('./feature/olympics-hungarian-team/olympics-hungarian-team.routing').then((m) => m.OLYMPICS_HUNGARIAN_TEAM_ROUTES),
        },
        {
          path: 'olimpia-2024/eremtablazat',
          loadChildren: () => import('./feature/olympics-medal-table/olympics-medal-table.routing').then((m) => m.OLYMPICS_MEDAL_TABLE_ROUTES),
        },
        {
          path: 'olimpia-2024/:athleteSlug',
          loadChildren: () => import('./feature/olympics-athlete-profile/olympics-athlete-profile.routing').then((m) => m.OLYMPICS_ATHLETE_PROFILE_ROUTES),
        },
        {
          // Hír
          path: ':categorySlug/:year/:month/:articleSlug',
          data: { omitGlobalPageView: true, skipSeoMetaCheck: true },
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        },
        {
          // Rovat REDIRECT MONTH
          path: ':categorySlug/:year/:date',
          loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
          data: { redirect: true },
        },
        /*{
        // Rovat REDIRECT YEAR
        path: ':categorySlug/:year',
        loadChildren: () =>
          import('./feature/category/category.routing').then(
            (m) => m.CATEGORY_ROUTES
          ),
        data: { redirect: true },
      },*/
        {
          // Régi hír
          path: ':categorySlug/:articleSlug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          path: 'idojaras',
          loadChildren: () => import('./feature/weather/weather.routing').then((m) => m.WEATHER_ROUTING),
        },
        {
          // Foundation and static route handler
          path: ':slug',
          data: { omitGlobalPageView: true },
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        },
      ] as Routes
    ).map((route: Route) => ({ ...route, canDeactivate: [...(route.canDeactivate ?? []), VignetteAdService] })),
  },
  {
    path: '**',
    redirectTo: '/404',
    data: { skipRouteLocalization: true },
  },
];
