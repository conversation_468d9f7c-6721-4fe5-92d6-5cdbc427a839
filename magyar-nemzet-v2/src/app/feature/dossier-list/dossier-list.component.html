<div class="wrapper">
  <mno-block-title-row [data]="titleRow" [isFullWidth]="true" [headingLevel]="1"></mno-block-title-row>

  <p *ngIf="rowAllCount === 0; else firstResult" class="dossier-notfound text-center">nincs tal<PERSON></p>

  <ng-template #firstResult>
    <article
      mno-article-card
      [data]="articleCards?.[0]"
      [styleID]="(isMobile$ | async) ? ArticleCardType.Img16TopTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWideLarge"
      [showThumbnail]="!!articles?.[0]?.thumbnail"
      [asResult]="!!(isMobile$ | async)"
      [imageLazyLoad]="false"
    ></article>
  </ng-template>
  <div class="dossier-ad" *ngIf="ads?.desktop?.roadblock_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>

  <div class="dossier-ad-sm" *ngIf="ads?.mobile?.mobilrectangle_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</div>

<div class="wrapper with-aside dossier-list-container">
  <div class="dossier-list d-flex flex-column left-column">
    <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
    <ng-container *ngFor="let article of articleCards | slice: 1; let i = index; trackBy: trackByFn; let isLast = last">
      <article
        mno-article-card
        [data]="article"
        [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
        [showThumbnail]="!!article?.thumbnailUrl || !!article?.thumbnailUrl43 || !!article?.thumbnail"
        [asResult]="!!(isMobile$ | async)"
        [imageLazyLoad]="i >= 7"
      ></article>
      <hr class="list-separator" *ngIf="!isLast" />
      <ng-container *ngIf="i === 2">
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      </ng-container>
    </ng-container>

    <mno-pager
      *ngIf="rowAllCount > rowOnPageCount"
      [rowAllCount]="rowAllCount"
      [rowOnPageCount]="rowOnPageCount"
      [isListPager]="true"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="5"
    ></mno-pager>
    <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>

  <app-sidebar></app-sidebar>
</div>
