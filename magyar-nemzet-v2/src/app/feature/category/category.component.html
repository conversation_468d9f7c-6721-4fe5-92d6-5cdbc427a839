<ng-container *ngIf="allData$ | async as allData">
  <div *ngIf="isEbEnabled" class="wrapper eb-wrapper">
    <a class="eb-header" routerLink="/labdarugo-eb-2024" title="Labdarúgó EB 2024">
      <img alt="Labdarúgó EB 2024" class="eb-header-logo" src="/assets/images/eb/logo_EB_2024.svg" />
      <img alt="" class="eb-header-overlay" src="/assets/images/eb/eb-static-bg-desktop-overlay.svg" />
    </a>
  </div>
  <div *ngIf="isOlympics2024" class="wrapper olympics-2024">
    <app-olympics-header></app-olympics-header>
  </div>
  <div [class.without-eb-header]="!isEbEnabled" [class.is-bayer-blog]="isBayerBlog$ | async" class="wrapper main-container">
    <section class="category full">
      <ng-container *ngIf="allData.pageData.columnSlug === 'cpac-hungary-2024'">
        <app-cpac></app-cpac>
      </ng-container>
      <div class="">
        <div *ngIf="allData.limitable.pageCurrent === 0 && (hasLayout$ | async); else topArticles" class="block">
          <app-layout
            *ngIf="allData.pageData?.layoutData as layoutData"
            [adPageType]="adPageType"
            [layoutType]="LayoutPageTypes.COLUMN"
            [configuration]="layoutData.content"
            [structure]="layoutData.struct"
          ></app-layout>
        </div>

        <ng-template #topArticles>
          <div *ngIf="allData.articles.length" class="narrow-wrapper">
            @if (isBayerBlog$ | async) {
              <mno-opinion-card [data]="allData.articles[0]" [isMobile]="isMobile$ | async" [styleID]="OpinionCardType.AuthorOpinionHeader"></mno-opinion-card>
            } @else {
              <app-module-heading
                [articles]="allData.articles"
                [isMobile]="(isMobile$ | async) === true"
                [isOpinion]="allData.pageData.columnSlug === 'velemeny'"
                [mobileStyleID]="ArticleCardType.ImgRightTagsTitleLeadBadge"
                [showTitle]="allData.limitable.pageCurrent !== 0"
                [title]="allData.pageData.columnTitle"
                color="white"
              >
              </app-module-heading>
            }
          </div>
        </ng-template>

        <div *ngIf="allData.ads?.desktop?.roadblock_1 as ad" class="desktop-ad-local dossier-ad">
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        </div>

        <div *ngIf="allData.ads?.mobile?.mobilrectangle_1 as ad" class="dossier-ad-sm">
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="medium_rectangle_1_tablet as ad" class="tablet-ad-local" [ad]="ad"></kesma-advertisement-adocean>
        </div>
      </div>
    </section>
  </div>
  <div [class.without-eb-header]="!isEbEnabled" [class.is-bayer-blog]="isBayerBlog$ | async" class="wrapper main-container">
    <section
      [ngClass]="allData.limitable.pageCurrent === 0 && (false === (isBayerBlog$ | async) || (hasLayout$ | async)) ? 'full' : 'with-sidebar'"
      class="category"
    >
      <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
      <div #articleList class="article-list wrapper narrow-wrapper">
        <div
          *ngIf="allData.limitable.pageCurrent === 0 && allData.pageData?.layoutData?.content?.length"
          [ngClass]="allData.limitable.pageCurrent === 0 ? 'full' : 'with-sidebar'"
          class="result-bar"
        >
          <p>{{ allData.pageData.columnTitle }} rovat további cikkei</p>
        </div>
        <ng-container
          *ngFor="
            let article of allData.articles
              | slice: (allData.limitable.pageCurrent === 0 && !allData.pageData?.layoutData?.content?.length ? ((isBayerBlog$ | async) ? 1 : 3) : 0);
            let i = index;
            let isLast = last
          "
        >
          <div class="blog-article-date" *ngIf="(isBayerBlog$ | async) && (isMobile$ | async)">
            {{ article.publishDate | dfnsFormat: 'MMMM. d. iiii HH:mm' }}
          </div>
          <article
            [data]="article"
            [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
            class="img-ratio-43"
            [imageLazyLoad]="(hasLayout$ | async) || i >= 5"
            mno-article-card
          ></article>

          <hr *ngIf="!isLast" class="list-separator" />

          <div *ngIf="i === 1" class="dossier-ad">
            <kesma-advertisement-adocean class="desktop-ad-local" *ngIf="allData.ads?.desktop?.roadblock_2 as ad" [ad]="ad"></kesma-advertisement-adocean>

            <kesma-advertisement-adocean *ngIf="medium_rectangle_2_tablet as ad" class="tablet-ad-local" [ad]="ad"></kesma-advertisement-adocean>

            <kesma-advertisement-adocean *ngIf="allData.ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 3" class="dossier-ad">
            <kesma-advertisement-adocean class="desktop-ad-local" *ngIf="allData?.ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>

            <kesma-advertisement-adocean *ngIf="medium_rectangle_3_tablet as ad" class="tablet-ad-local" [ad]="ad"></kesma-advertisement-adocean>

            <kesma-advertisement-adocean *ngIf="allData?.ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
          </div>
        </ng-container>
        <ng-container *ngIf="allData.limitable as limitable">
          <mno-pager
            *ngIf="limitable?.pageMax! > 0"
            [allowAutoScrollToTop]="true"
            [hasSkipButton]="true"
            [isListPager]="true"
            [maxDisplayedPages]="5"
            [rowAllCount]="limitable?.rowAllCount!"
            [rowOnPageCount]="limitable?.rowOnPageCount!"
          ></mno-pager>
        </ng-container>

        <kesma-advertisement-adocean class="desktop-ad-local" *ngIf="allData.ads?.desktop?.roadblock_4 as ad" [ad]="ad"></kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="medium_rectangle_2_tablet as ad" class="tablet-ad-local" [ad]="ad"></kesma-advertisement-adocean>

        <kesma-advertisement-adocean *ngIf="allData.ads?.mobile?.mobilrectangle_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
      </div>
    </section>
    <div class="sidebar">
      <app-sidebar
        [adPageType]="'column_' + adPageType"
        [categorySlug]="allData.pageData.columnSlug"
        [excludedIds]="allData.pageData.excludedIds"
      ></app-sidebar>
    </div>
  </div>
</ng-container>
