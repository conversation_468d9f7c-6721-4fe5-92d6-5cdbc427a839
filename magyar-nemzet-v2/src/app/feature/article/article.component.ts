import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ForOf, NgIf, <PERSON><PERSON><PERSON>, NgSwitchCase, NgT<PERSON>plateOutlet, SlicePipe } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostBinding,
  OnDestroy,
  OnInit,
  signal,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
  ArticleSchema,
  backendDateToDate,
  EmbeddingService,
  FormatDatePipe,
  IMetaData,
  PublishDatePipe,
  SchemaOrgService,
  SeoService,
  StorageService,
  UtilService,
} from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertsMeta,
  ALL_BANNER_LIST,
  AnalyticsService,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleLanguage,
  ArticleReview,
  ArticleReviewBody,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  BackendArticleSearchResult,
  backendDateToUTC,
  BackendRecommendationsData,
  BackendRecommendedArticle,
  BasicDossier,
  BlockTitle,
  buildArticleUrl,
  ChampionshipSchedule,
  ComponentData,
  DossierArticleShort,
  DossierCardTypes,
  DossierData,
  EBPortalEnum,
  EbResultsComponent,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  FakeBool,
  GalleryData,
  GalleryElementData,
  GalleryRecommendationData,
  getStructuredDataForArticle,
  GoogleNewsComponent,
  GoogleNewsData,
  KesmaEbNavigatorComponent,
  LayoutPageType,
  mapBackendArticleDataToArticleCard,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  NEWSLETTER_COMPONENT_TYPE,
  OlimpiaNavigatorComponent,
  OlimpicPortalEnum,
  PAGE_TYPES,
  PortalConfigSetting,
  previewBackendArticleToArticleCard,
  RedirectService,
  ScriptLoaderService,
  SecondaryFilterAdvertType,
  SorozatvetoArticleCard,
  SorozatvetoOpinionCard,
  SponsoredTag,
  Sponsorship,
  Tag,
  toBool,
  VideoComponentObject,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { BehaviorSubject, combineLatest, first, forkJoin, Observable, of, Subject, take } from 'rxjs';
import { catchError, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import {
  ArticleCardComponent,
  ArticleCardType,
  ArticleTextComponent,
  BadgeDirective,
  BlockTitleRowComponent,
  BlockTitleRowType,
  DossierCardComponent,
  ExternalRecommendationsComponent,
  IconComponent,
  OpinionAuthorComponent,
  OpinionCardComponent,
  OpinionCardType,
  OpinionNewsletterBoxComponent,
  OpinionNewsletterBoxType,
  PagerComponent,
  PromoBlockComponent,
  QuizComponent,
  SliderGalleryComponent,
  SocialShareComponent,
  VotingComponent,
  WysiwygBoxComponent,
} from 'src/app/shared';
import { EbHeaderLogoComponent } from 'src/app/shared/components/eb/eb-header-logo/eb-header-logo.component';
import { FoundationRecommendationComponent } from 'src/app/shared/components/foundation-recommendation/foundation-recommendation.component';
import { NativtereloComponent } from 'src/app/shared/components/nativterelo/nativterelo.component';
import { NewsletterPopupComponent } from 'src/app/shared/components/newsletter-popup/newsletter-popup.component';
import { OlympicsHeaderComponent } from 'src/app/shared/components/olympics-header/olympics-header.component';
import { RecommendationBlockComponent } from 'src/app/shared/components/recommendation-block/recommendation-block.component';
import { OlimpiaService } from 'src/app/shared/services/olimpia.service';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { SportResultService } from 'src/app/shared/services/sport-result.service';
import { environment } from 'src/environments/environment';
import {
  ApiService,
  ArticleService,
  ConfigService,
  ContentLanguageService,
  defaultMetaInfo,
  DossierService,
  ElectionsService,
  GalleryService,
  opinionNewsletterInfoLink,
  PersonalizedRecommendationService,
  SharedService,
} from '../../shared';
import { SponsoredTagBoxComponent } from '../../shared/components/sponsored-tag-box/sponsored-tag-box.component';
import { GlossaryProcessorService } from '../glossary/glossary-processor.service';
import { Glossary } from '../glossary/glossary.definitions';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleResolverData } from './article.definitions';
import { ArticleAdultComponent } from './components/article-adult/article-adult.component';
import { ArticleHeaderComponent } from './components/article-header/article-header.component';
import { InfoBlockComponent } from './components/info-block/info-block.component';

const TELEGRAM_SCRIPT = 'https://telegram.org/js/telegram-widget.js?21';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrls: ['./article.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AutoArticleBodyAdService, FormatDatePipe],
  imports: [
    AdvertisementAdoceanComponent,
    PagerComponent,
    ArticleCardComponent,
    LayoutComponent,
    OlimpiaNavigatorComponent,
    AsyncPipe,
    RouterLink,
    NgTemplateOutlet,
    OlympicsHeaderComponent,
    RecommendationBlockComponent,
    ArticleVideoComponent,
    SlicePipe,
    PublishDatePipe,
    ArticleHeaderComponent,
    OpinionAuthorComponent,
    ArticleAdultComponent,
    IconComponent,
    SocialShareComponent,
    PromoBlockComponent,
    NewsletterPopupComponent,
    NgSwitch,
    NgSwitchCase,
    BlockTitleRowComponent,
    ArticleTextComponent,
    KesmaEbNavigatorComponent,
    FormatDatePipe,
    OpinionCardComponent,
    OpinionNewsletterBoxComponent,
    FoundationRecommendationComponent,
    InfoBlockComponent,
    DossierCardComponent,
    SliderGalleryComponent,
    GoogleNewsComponent,
    NativtereloComponent,
    ElectionsBoxComponent,
    SidebarComponent,
    EbResultsComponent,
    VotingComponent,
    EbHeaderLogoComponent,
    NgIf,
    NgFor,
    NgForOf,
    BadgeDirective,
    SponsoredTagBoxComponent,
    QuizComponent,
    WysiwygBoxComponent,
    ExternalRecommendationsComponent,
  ],
})
export class ArticleComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('dataTrigger') readonly dataTrigger: ElementRef;

  @HostBinding('class.is-opinion') isOpinion = false;
  @HostBinding('class.is-minute-to-minute') isMinuteToMinute = false;
  @HostBinding('class.news-reception') isSponsored = false;
  @ViewChildren('minuteBlock', { read: ElementRef }) minuteBlockElements: ElementRef<HTMLAnchorElement>[];

  articleResponse: Article & ArticleReview;
  sponsoredTag?: SponsoredTag;
  articleCard: ArticleCard;
  recommendationData: BackendRecommendationsData;
  recommendedHighPriorityArticles: BackendRecommendedArticle[];
  recommendedArticles: BackendRecommendedArticle[];
  recommendedLowPriorityArticles: BackendRecommendedArticle[];
  id: string;
  articleSlug: string;
  tag: string;
  category: string;
  categorySlug: string;
  title: string;
  publicAuthor?: string;
  lead: string;
  publishDate: Date;
  tags: Tag[] = [];
  avatar?: string;
  lastUpdated: string;
  subTitle?: string;
  thumbnail?: string;
  body: ComponentData[][] = [];
  minuteToMinuteState: MinuteToMinuteState = MinuteToMinuteState.NOT;
  minuteToMinuteBlocks: MinuteToMinuteBlock[];
  articleLanguages: ArticleLanguage[];
  articleSource?: string;
  dossier: DossierData;
  dossierCount?: number;
  isPagination: boolean;
  currentPage$: Observable<number>;
  isMobile$: Observable<boolean>;
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  isUserAdultChoice: boolean;
  url: string;
  glossary: Glossary[] = [];
  labels: string[] = [];
  isInterview = false;
  useTopBanner = false;
  isVideo = false;
  isGallery = false;
  isEB2024 = false;
  @HostBinding('class.is-sorozatveto') isSorozatveto = false;
  readonly ArticleCardType = ArticleCardType;
  readonly OpinionCardType = OpinionCardType;
  readonly BlockTitleRowType = BlockTitleRowType;
  readonly ArticleBodyType = ArticleBodyType;
  readonly OpinionNewsletterBoxType = OpinionNewsletterBoxType;
  readonly DEFAULT_AVATAR = '/assets/images/logo-mno-small-padded-dark.svg';
  videoLead?: VideoComponentObject;
  videoLeadPosition?: 'above_title' | 'below_title';
  brand: string;
  sorozatvetoArticles: ArticleCard[] = [];
  velemenyvaroArticles: ArticleCard[] = [];
  galleries: Record<string, GalleryData> = {};
  sponsorship?: Sponsorship;
  highlightedGallery: GalleryElementData;
  readonly DossierCardTypes = DossierCardTypes;
  highPriorityRecommendationTitle: BlockTitle;
  lowPriorityRecommendationTitle: BlockTitle;
  sorozatvetoTitle: BlockTitle = {
    text: 'Sorozatvető',
    url: '/rovat/sorozatveto',
    urlName: 'Tovább az összes cikkhez',
  };
  velemenyvaroTitle: BlockTitle = {
    text: 'Véleményváró',
    url: '/rovat/velemenyvaro',
    urlName: 'Tovább az összes cikkhez',
  };
  authorArticlesTitle: BlockTitle = {
    text: 'A szerző további cikkei',
    url: '/szerzo/',
    urlName: 'Tovább az összes cikkhez',
  };
  galleryArticlesTitle: BlockTitle = {
    text: 'Galéria ajánló',
    url: '/galeriak/',
    urlName: 'Tovább az összes galériához',
  };
  socialInfo = {
    facebookLink: 'https://www.facebook.com/magyarnemzet.hu',
    instagramLink: 'https://www.instagram.com/magyarnemzet.hu/?hl=en',
    youtubeLink: 'https://www.youtube.com/@magyarnemzet_hivatalos',
    twitterLink: 'https://twitter.com/MagyarNemzetOn',
    videaLink: 'https://videa.hu/csatornak/magyar-nemzet-404',
  };
  googleNews: GoogleNewsData = {
    portalName: 'Magyar Nemzet',
    googleNewsUrl: 'https://news.google.com/publications/CAAqBwgKMMfOsgsw1OnJAw?hl=hu&gl=HU&ceid=HU%3Ahu',
  };
  authorArticles$ = new Observable<ArticleCard[]>();
  galleryArticles$ = new Observable<ArticleCard[]>();
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  opinionInfoLink = opinionNewsletterInfoLink;
  ebImportantSidebarLayout$ = this.route.data.pipe(map(({ articlePageData }) => articlePageData?.['ebImportantSidebarLayout']));
  advertisementMeta: AdvertsMeta;
  adverts?: ArticleAdvertisements;
  ottboxAds?: ArticleAdvertisements;
  interrupter?: ArticleAdvertisements;
  articleRecommendationCache: Record<string, BehaviorSubject<ArticleCard | null>> = {};
  readonly LayoutPageType = LayoutPageType;
  isEBEnabled = false;
  ElectionsBoxStyle = ElectionsBoxStyle;
  readonly EBPortalEnum = EBPortalEnum;
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly hideOnMobileApp: boolean = true;
  schedules: Record<string, ChampionshipSchedule[]> = {};
  private isOlimpia2024: boolean;
  private currentPageSubject$: BehaviorSubject<number>;
  private readonly destroy$ = new Subject<boolean>();
  private cannonicalUrl: string;
  isExceptionAdvertEnabled: boolean;

  readonly voteCache = this.voteService.voteCache;

  readonly reviewOpinion = signal<SorozatvetoOpinionCard<ArticleReviewBody[]> | undefined>(undefined); // Sorozatvető > Vélemény

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly embedding: EmbeddingService,
    private readonly dossierService: DossierService,
    private readonly schema: SchemaOrgService,
    private readonly formatDate: FormatDatePipe,
    private readonly analyticsService: AnalyticsService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly glossaryService: GlossaryProcessorService,
    private readonly sharedService: SharedService,
    private readonly configService: ConfigService,
    private readonly storage: StorageService,
    private readonly galleryService: GalleryService,
    private readonly apiService: ApiService,
    private readonly articleService: ArticleService,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    private readonly portalConfigService: PortalConfigService,
    private readonly sportResultService: SportResultService,
    public readonly electionsService: ElectionsService,
    private readonly contentLanguageService: ContentLanguageService,
    private readonly scriptLoader: ScriptLoaderService,
    private readonly voteService: VoteService,
    public readonly olimpiaService: OlimpiaService,
    public readonly router: Router,
    private readonly redirectService: RedirectService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService
  ) {
    this.isEBEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);

    this.hideOnMobileApp = this.configService.isMobileApp;
  }

  get isOlympics2024(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS);
  }

  get sorozatvetoArticle(): SorozatvetoArticleCard {
    return this.articleResponse as unknown as SorozatvetoArticleCard;
  }

  get highlightedGalleryData(): GalleryData {
    return this.galleries[this.highlightedGallery.details[0].value.id];
  }

  private get currentPage(): number {
    const queryPageNum = parseInt(this.route.snapshot.queryParamMap.get('page') ?? '1');
    if (!isNaN(queryPageNum)) {
      return queryPageNum;
    }
    return -1;
  }

  isBrowser(): boolean {
    return this.utilsService.isBrowser();
  }

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.scriptLoader.loadScript(TELEGRAM_SCRIPT, false);
    this.sharedService.setIsArticlePage(true);

    this.route.data
      .pipe(takeUntil(this.destroy$))
      .pipe(
        map((res) => {
          const {
            article: { data, meta },
            url,
            recommendations,
            sorozatvetoArticles,
            velemenyvaroArticles,
            foundationTagSlug,
            foundationTagTitle,
          } = res['articlePageData'] as ArticleResolverData;

          this.foundationTagSlug = foundationTagSlug;
          this.foundationTagTitle = foundationTagTitle;

          this.autoArticleBodyAd.init(this.articleResponse?.body);
          this.body = this.autoArticleBodyAd.autoAd() as unknown as ComponentData[][];

          this.articleResponse = {
            ...this.articleResponse,
            body: this.#prepareArticleBody(this.body as unknown as ArticleBody[]),
          } as unknown as Article & ArticleReview;

          this.articleResponse = { ...data, body: this.#prepareArticleBody(this.body as unknown as ArticleBody[]) } as any as Article & ArticleReview;
          this.sponsoredTag = meta?.['sponsoredTag'];
          this.articleCard = mapBackendArticleDataToArticleCard(data as any as BackendArticleSearchResult);
          this.url = url;

          this.setAdMetaAndPageType(this.articleResponse);

          this.articleSlug = this.articleResponse?.slug;
          this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.articleResponse?.isAdultsOnly;
          const fallbackCanonical = `${this.seo.hostUrl}/${url || ''}`;
          this.cannonicalUrl = url?.includes('kultura/')
            ? fallbackCanonical
            : this.articleResponse.seo?.seoCanonicalUrl || this.articleResponse?.canonicalUrl || fallbackCanonical;
          // this.isShortNews = this.articleResponse.isShortNewsType;
          this.seo.updateCanonicalUrl(this.cannonicalUrl ?? '', { addHostUrl: false, skipSeoMetaCheck: true });
          this.loadEmbeddedGalleries();
          this.loadSchedulesGroupedByRoundList();
          this.recommendationData = recommendations?.data;
          this.sorozatvetoArticles = (sorozatvetoArticles?.data ?? []) as unknown as ArticleCard[];
          this.velemenyvaroArticles = (velemenyvaroArticles?.data ?? []) as unknown as ArticleCard[];
          if (!this.articleResponse) {
            return {
              withoutAds: false,
              dossierSlug: undefined,
            };
          }
          const {
            id,
            primaryColumn,
            title,
            publicAuthor,
            lead,
            tags,
            publishDate,
            lastUpdated,
            avatar,
            subTitle,
            thumbnail,
            body,
            minuteToMinute,
            minuteToMinuteBlocks,
            languages,
            articleSource,
            excerpt,
            isOpinion,
            isInterviewType,
            videoLead,
          } = this.articleResponse || {};
          if (meta?.['sponsorship']) {
            // this.configService.setSponsorship(meta['sponsorship'])
            this.configService.clearSponsorship();
            this.sponsorship = meta['sponsorship'] as Sponsorship;
            this.isSponsored = true;
          }
          ({ video: this.videoLead, position: this.videoLeadPosition } = videoLead ?? {
            video: undefined,
            position: undefined,
          });

          this.isOpinion = isOpinion ?? false;
          if (isOpinion) {
            this.fetchAuthorArticles();
          }
          this.isVideo = !!this.videoLead?.videaUrl;
          this.isSorozatveto = !!this.sorozatvetoArticle?.reviews?.length;
          this.isInterview = isInterviewType ?? false;
          this.glossary = meta?.['glossary'];

          const reviewSlug = this.route.snapshot.paramMap.get('reviewSlug');
          this.reviewOpinion.set(this.sorozatvetoArticle.reviews?.find((review) => review.slug === reviewSlug));

          this.id = id;
          this.category = primaryColumn?.title;
          this.categorySlug = primaryColumn?.slug;
          this.title = title;
          this.publicAuthor = publicAuthor;
          this.lead = lead || (excerpt as string);
          this.tags = tags;
          this.isEB2024 = !!this.tags.find((tag: Tag) => tag.title === 'Eb 2024');
          this.publishDate = publishDate ?? new Date(); // backendDateToDate(publishDate as unknown as string) ?? new Date;
          this.lastUpdated = lastUpdated as unknown as string;
          this.avatar = avatar || 'assets/images/magyar-nemzet-logo-circle.png';
          this.subTitle = subTitle ?? undefined;
          this.thumbnail = thumbnail;
          this.minuteToMinuteState = minuteToMinute;
          if (this.minuteToMinuteState !== MinuteToMinuteState.NOT) {
            this.isMinuteToMinute = true;
          }
          this.configService.setHeader({
            isDark: isOpinion || isInterviewType || this.isMinuteToMinute,
            breakingColor: this.isMinuteToMinute || this.isSorozatveto ? 'blue' : 'red',
          });

          this.minuteToMinuteBlocks = minuteToMinuteBlocks ?? [];
          this.articleLanguages = languages ?? [];
          this.articleSource = articleSource;

          this.glossaryService.init(this.glossary, this.seo.hostUrl + '/sportlexikon');
          this.body = this.splitArticleToPages(this.glossaryService.linkGlossaryItems(body as unknown as ComponentData[]));
          this.isGallery = !!this.highlightedGallery?.details?.[0]?.value?.id;
          if (this.isGallery) {
            this.fetchGalleryArticles();
          }
          this.isPagination = this.body.length > 1;
          const pageNum = this.currentPage;
          if (pageNum > this.body.length || pageNum < 1 || isNaN(pageNum)) {
            const redirectUrl = buildArticleUrl(this.articleResponse).join('/').replace('//', '');
            this.redirectService.redirectOldUrl(redirectUrl, false, 302);
          }
          this.currentPageSubject$ = new BehaviorSubject<number>(pageNum);
          this.currentPage$ = this.currentPageSubject$.asObservable();

          this.labels = ([...(this.articleResponse?.tags ?? []).map(({ title }) => title)].filter((s) => !!s) as string[]) ?? [];

          if (!this.recommendationData) {
            return {
              withoutAds: this.articleResponse?.withoutAds,
              dossierSlug: this.articleResponse?.dossier?.slug,
            };
          }
          const { highPriorityArticles, lowPriorityArticles } = this.recommendationData;
          this.recommendedHighPriorityArticles = highPriorityArticles ?? [];
          this.recommendedLowPriorityArticles = lowPriorityArticles ?? [];
          this.recommendedArticles = this.recommendedHighPriorityArticles.concat(this.recommendedLowPriorityArticles);
          if (this.recommendedArticles.length > 6) {
            this.recommendedArticles = this.recommendedArticles.slice(0, 6);
          }
          this.highPriorityRecommendationTitle = {
            text: this.isMinuteToMinute ? `További ${this.articleCard.columnTitle} hírek` : 'A téma legfrissebb hírei',
            url: `/rovat/${this.articleCard.category?.slug}`,
            urlName: 'Tovább az összes cikkhez',
          };
          this.lowPriorityRecommendationTitle = {
            text: 'Címoldalról ajánljuk',
            url: '/',
            urlName: 'Tovább az összes cikkhez',
          };

          this.setMetaData();
          this.scrollTopAfterLoad();
          this.applySchemaOrg(this.articleResponse);
          this.voteService.initArticleVotes(this.articleResponse);

          // Not the nicest way to do it, but BE does not send information about the language of the article
          if (this.articleResponse.primaryColumn?.slug === 'english') {
            this.contentLanguageService.setLang('en');
          } else {
            this.contentLanguageService.setLang('hu');
          }
          this.processAlternateLangLinks();

          setTimeout(() => {
            this.analyticsService.sendPageView({
              pageCategory: this.category,
              customDim2: this.articleResponse?.topicLevel1,
              customDim1: this.articleResponse?.aniCode,
              //
              title: this.title,
              articleSource: this.articleSource ? this.articleSource : 'no source',
              publishDate: this.formatDate.transform(this.articleResponse?.publishDate ?? '', 'dateTime'),
              lastUpdatedDate: this.formatDate.transform(this.lastUpdated ? this.lastUpdated : (this.articleResponse?.publishDate ?? ''), 'dateTime'),
              researchCategory: this.articleResponse?.seo?.seoCategorySelector,
            });
          }, 0);

          this.useTopBanner =
            !!(
              this.isInterview ||
              this.isOpinion ||
              this.articleCard.hasGallery ||
              !!((this.articleCard.isVideo as any) * 1) ||
              this.articleCard.isVideoType
            ) || this.isMinuteToMinute;

          return {
            withoutAds: this.articleResponse?.withoutAds,
            dossierSlug: this.articleResponse?.dossier?.slug,
          };
        }),
        switchMap(({ dossierSlug }) =>
          dossierSlug
            ? this.dossierService.getDossier(dossierSlug)
            : of({
                data: [],
                meta: { dataCount: undefined },
              })
        )
      )
      .subscribe(({ data: dossierArticles, meta: dossierMeta }) => {
        this.dossierCount = dossierMeta?.dataCount;
        if (dossierArticles?.length) {
          const dossierCategory = this.articleResponse?.dossier as BasicDossier;

          const filteredDossier: BasicDossier[] = dossierArticles.filter((dossier) => dossier?.slug !== this.articleSlug);
          const secondaryArticles: BasicDossier[] = filteredDossier.slice(1, 4);
          const mainArticle = filteredDossier[0];

          this.dossier = {
            secondaryArticles: secondaryArticles as unknown as DossierArticleShort[],
            mainArticle: mainArticle as unknown as DossierArticleShort,
            ...dossierCategory,
          } as DossierData;
        }
      });

    this.route.params.pipe(takeUntil(this.destroy$)).pipe(
      tap((params) => {
        this.brand = params['brand'];
      })
    );

    this.initAds();
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();

    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.triggerDataLayer();
          }
        });
      }, 1000);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    this.adStore.onArticleDestroy();
    Object.values(this.articleRecommendationCache).map((cachedCard) => cachedCard.complete());
    this.articleRecommendationCache = {};
    this.olimpiaService.setIsOlimpiaMainOrArticlePage(false);
  }

  get canDisplayCover(): boolean {
    return this.articleResponse?.primaryColumn?.slug !== 'keresztrejtveny';
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adStore.setIsAdultPage(isUserAdult);
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }

  getReviewBody(opinionCard: SorozatvetoOpinionCard<ArticleReviewBody[]>): string[] {
    const bodyParts = (Array.isArray(opinionCard.body) ? opinionCard.body : []).map(({ details }) => details.map(({ value }) => value));
    return ([] as string[]).concat(...bodyParts);
  }

  getReviewBlock(opinionCard: SorozatvetoOpinionCard<ArticleReviewBody[]>): string {
    return typeof opinionCard.body === 'string' ? opinionCard.body : '';
  }

  navigateToGallery(index: number, galleryData: GalleryData): void {
    if (!this.utilsService.isBrowser() || !galleryData?.slug) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', galleryData.slug, ...(index || index === 0 ? [index + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }

  reviewBlockIsArray(opinionCard: SorozatvetoOpinionCard<ArticleReviewBody[]>): boolean {
    return !!Array.isArray(opinionCard);
  }

  getGallery(element: GalleryElementData): GalleryData {
    return this.galleries[element?.details?.[0]?.value?.id];
  }

  onSetIsOlimpia(tags: Tag[]): void {
    const tagSlugs = tags?.map(({ slug }) => slug);

    this.isOlimpia2024 = tagSlugs?.includes('olimpia-2024-2');
    this.olimpiaService.setIsOlimpiaMainOrArticlePage(this.isOlimpia2024);
  }

  onMinuteBlockClick({ index, event }: { index: number; event: MouseEvent }): void {
    event.preventDefault();
    const lookupId = `minute-block-${index}`;
    const block = this.minuteBlockElements.find(({ nativeElement: { id } }) => id === lookupId);
    block?.nativeElement?.scrollIntoView({
      block: 'start',
      behavior: 'smooth',
    });
  }

  onSubscribe(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.ARTICLE_END);
  }

  getArticleRecommendation(element: ComponentData): Observable<ArticleCard> {
    if (!element) return of({} as ArticleCard);
    const baseData: Article = element.details[0]?.value;
    if (this.articleRecommendationCache[baseData.id]) {
      return this.articleRecommendationCache[baseData.id].asObservable().pipe(filter((card) => !!card)) as Observable<ArticleCard>;
    }
    this.articleRecommendationCache[baseData.id] = new BehaviorSubject<ArticleCard | null>(null);
    const publishDateParts = (baseData.publishDate as any as string)?.split('-');

    this.articleService
      .getArticle(baseData.primaryColumn?.slug, publishDateParts?.[0], publishDateParts?.[1], baseData?.slug as string)
      .pipe(
        map(
          ({ data }) =>
            ({
              ...baseData,
              columnSlug: baseData.primaryColumn?.slug,
              thumbnailUrl: data?.thumbnail,
            }) as ArticleCard
        ),
        take(1)
      )
      .subscribe((card) => {
        this.articleRecommendationCache[baseData.id].next(card);
      });
    return this.articleRecommendationCache[baseData.id].asObservable().pipe(filter((card) => !!card)) as Observable<ArticleCard>;
  }

  getArticleLink(recommenderData: ArticleCard): string[] {
    return buildArticleUrl(recommenderData);
  }

  setAdMetaAndPageType(articleResponse: Article & ArticleReview): void {
    this.adPageType = `column_${articleResponse?.primaryColumn?.slug}`;
    this.adStore.setArticleParentCategory(this.adPageType);
    this.adStore.getAdvertisementMeta(articleResponse?.tags, articleResponse?.isAdultsOnly);
  }

  onVotingSubmit(votedId: string, voteData: VoteDataWithAnswer): void {
    this.voteService
      .onVotingSubmit(votedId, voteData)
      .pipe(first())
      .subscribe(() => this.changeRef.detectChanges());
  }

  getReviewLink(review: SorozatvetoOpinionCard): string {
    let baseUrl = this.reviewOpinion() ? this.router.url.split('/').slice(0, -1).join('/') : this.router.url;

    if (baseUrl?.includes('?')) {
      baseUrl = baseUrl.split('?')[0];
    }

    return `${baseUrl}/${review.slug}`;
  }

  private setMetaData(): void {
    const { lead, thumbnail, publicAuthor, publishDate } = this.articleResponse || {};
    if (!this.articleResponse) {
      return;
    }
    const { seo } = this.articleResponse;
    const title = seo?.seoTitle || this.articleResponse.title;

    const metaData: IMetaData = {
      title: title,
      description: this.articleResponse.seo?.seoDescription || lead || defaultMetaInfo?.ogTitle,
      ogTitle: this.articleResponse.title,
      ogImage: thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate as unknown as string,
      robots: seo?.seoRobotsMeta ?? 'index, follow, max-image-preview:large',
    };

    this.seo.setMetaData(metaData, { skipSeoMetaCheck: true });
  }

  private scrollTopAfterLoad(): void {
    if (this.utilsService.isBrowser()) {
      window.scrollTo(0, 0);
    }
  }

  private applySchemaOrg(articleResponse: Article & ArticleReview): void {
    const newsArticle: ArticleSchema = getStructuredDataForArticle(
      {
        ...articleResponse,
        rawLastUpdated: backendDateToUTC(articleResponse.rawLastUpdated as unknown as string) as string,
        rawPublishDate: backendDateToUTC(articleResponse.rawPublishDate as unknown as string) as string,
      },
      this.seo.currentUrl,
      environment?.siteUrl ?? '',
      {
        hasAuthorPageSlug: true,
      }
    );
    this.schema.removeStructuredData();
    this.schema.insertSchema(newsArticle);
  }

  private triggerDataLayer(): void {
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          observer.unobserve(this.dataTrigger?.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger?.nativeElement);
  }

  private sendEcommerceEvent(): void {
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.id}`,
      title: this.title,
      articleSlug: this.articleResponse?.slug ?? 'cikk-elonezet',

      category: this.category,
      articleSource: this.articleSource ? this.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.articleResponse?.publishDate ?? '', 'dateTime'),
      lastUpdatedDate: this.formatDate.transform(this.lastUpdated ? this.lastUpdated : (this.articleResponse?.publishDate ?? ''), 'dateTime'),
    });
  }

  private splitArticleToPages(data: ComponentData[]): ComponentData[][] {
    if (data?.[0]?.type === ArticleBodyType.Gallery) {
      this.highlightedGallery = data.shift() as unknown as GalleryElementData;
    }
    const arr: ComponentData[][] = [[]];
    let i = 0;
    data.forEach((item) => {
      if (item.type === 'Pagination.Pagination') {
        i++;
        arr[i] = [];
        return;
      }
      if (!Array.isArray(arr[i])) {
        arr[i] = [];
      }
      arr[i].push(item);
    });
    return arr;
  }

  private loadSchedulesGroupedByRoundList(): void {
    const detailIds: string[] = [];
    const bodyElements = this.articleResponse?.body ?? [];
    const schedules$ = bodyElements
      .filter(({ type }) => type === ArticleBodyType.SportCompetitionSchedules)
      .map(({ details, id }) => {
        detailIds.push(id);
        const [_, data] = details;
        const scheduleIds = data?.value.map((value: ArticleBody) => value?.id);
        return this.sportResultService.getSchedulesGroupedByRoundList(scheduleIds);
      });

    forkJoin(schedules$)
      .pipe(takeUntil(this.destroy$))
      .subscribe((schedules) => {
        schedules.forEach((schedule: Record<string, ChampionshipSchedule[]>, index) => {
          this.schedules[detailIds[index]] = Object.keys(schedule)
            .map((key: string) =>
              schedule[key].map((value: ChampionshipSchedule) => ({
                ...value,
                round: key,
              }))
            )
            .flat();
        });
        this.changeRef.markForCheck();
      });
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = (this.articleResponse?.body as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details?.[0]?.value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.destroy$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.changeRef.markForCheck();
        });
      });
  }

  private fetchAuthorArticles(): void {
    if (!this.articleResponse?.publicAuthorSlug) {
      return;
    }
    this.authorArticlesTitle.url = '/szerzo/' + this.articleResponse.publicAuthorSlug;
    this.authorArticles$ = this.apiService
      .getSearch(
        {
          authorSlug: this.articleResponse.publicAuthorSlug,
          global_filter: '',
          // 'content_types[]': 'opinion',
          'excludedArticleIds[]': this.articleResponse.id,
        },
        0,
        4
      )
      .pipe(map(({ data }) => data))
      .pipe(
        map((rec) =>
          rec.map(
            (rec) =>
              ({
                ...rec,
                publishDate: backendDateToDate(rec.publishDate as string, true) as Date,
              }) as unknown as ArticleCard
          )
        )
      );
  }

  private processAlternateLangLinks(): void {
    const languages = this.articleResponse.languages;
    languages?.forEach((languageEntry) => {
      const language = languageEntry.languageSlug || languageEntry.columnSlug === 'english' ? 'en' : 'hu';
      const url = buildArticleUrl({
        ...this.articleResponse,
        columnSlug: languageEntry.columnSlug,
        publishDate: languageEntry.articlePublishDate,
        slug: languageEntry.articleSlug,
      });
      this.contentLanguageService.addAlternateLangLink(language, `${environment.siteUrl}/${url.slice(1).join('/')}`);
    });
  }

  private fetchGalleryArticles(): void {
    const gallerySlug = this.highlightedGallery.details?.[0]?.value?.slug;
    this.galleryArticles$ = this.galleryService
      .getGalleryRecommendationDatas(gallerySlug)
      .pipe(
        catchError((_) => {
          return of({ data: [] });
        })
      )
      .pipe(map(({ data }) => data))
      .pipe(
        map((galleries: (GalleryRecommendationData & { isAdult?: FakeBool })[]) =>
          galleries.map(
            ({
              title,
              slug,
              publicDate,
              // tags,
              thumbnailUrl,
              photographer,
              count,
              isAdult,
            }) =>
              ({
                title,
                slug,
                length: count,
                publishDate: (typeof publicDate as any) === 'string' ? backendDateToDate(publicDate as any as string) : publicDate,
                tags: [],
                thumbnail: { url: thumbnailUrl },
                articleSource: photographer,
                isAdultsOnly: toBool(isAdult),
              }) as ArticleCard
          )
        )
      );
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.interrupter = undefined;
    this.changeRef.detectChanges();
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: previewBackendArticleToArticleCard(detail.value),
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }

  private initAds(): void {
    (
      combineLatest([
        this.route.data as Observable<{
          articlePageData: ArticleResolverData;
        }>,
        this.adStore.isAdult.asObservable(),
      ]) as Observable<[{ articlePageData: ArticleResolverData }, boolean]>
    )
      .pipe(takeUntil(this.destroy$))
      .pipe(
        tap(() => this.resetAds()),
        map<[{ articlePageData: ArticleResolverData }, boolean], boolean | undefined>(([{ articlePageData }]) => {
          const articleResponse = articlePageData.article.data as unknown as Article & ArticleReview;
          this.setAdMetaAndPageType(articleResponse);
          this.onSetIsOlimpia(articleResponse?.tags || []);

          this.adStore.getAdvertisementMeta(articleResponse?.tags, articleResponse.isAdultsOnly);
          this.isExceptionAdvertEnabled = articleResponse?.isExceptionAdvertEnabled;

          return articleResponse?.withoutAds;
        }),
        switchMap((withoutAds) => {
          withoutAds ? this.adStore.disableAds() : this.adStore.enableAds();
          return this.adStore.advertisemenets$;
        })
      )
      .subscribe((adverts): void => {
        this.adverts = this.adStore.separateAdsByMedium(adverts, this.adPageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);
        this.ottboxAds = this.adStore.separateAdsByMedium(adverts);
        this.interrupter = this.adStore.separateAdsByMedium(adverts);
        this.adStore.onArticleLoaded();
        this.changeRef.detectChanges();
      });
  }
}
