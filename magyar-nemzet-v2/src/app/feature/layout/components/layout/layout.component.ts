import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>If, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  AnalyticsService,
  Article,
  ArticleCard,
  Author,
  BasicDossier,
  BlockWrapperTemplateData,
  BreakingNews,
  EbDidYouKnow,
  EBPortalEnum,
  GalleryData,
  GalleryDetails,
  HtmlEmbedComponent,
  ImageLayoutData,
  KoponyegComponent,
  KoponyegDefinitions,
  KoponyegType,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutDataExtractor,
  LayoutElement,
  LayoutElementColumn,
  LayoutElementContent,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  MinuteToMinuteBlock,
  NEWSLETTER_COMPONENT_TYPE,
  PAGE_TYPES,
  PortalConfigSetting,
  provideLayoutDataExtractors,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  SponsoredBoxComponent,
  SponsoredVotingComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { BehaviorSubject, forkJoin, Observable, of, Subject, takeUntil } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService, BayerBlogService, ConfigService, DossierService, GalleryService, opinionNewsletterInfoLink, QuizComponent } from '../../../../shared';
import { HttpClient } from '@angular/common/http';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import {
  ArticleCardComponent,
  ArticleCardType,
  ArticlePageImageComponent,
  BayerBlogBlockComponent,
  BlockTitleRowComponent,
  BlogRecommendationComponent,
  DossierCardComponent,
  DossierCardStyleTypes,
  EbService,
  MostReadComponent,
  NewspaperBoxSidebarComponent,
  OpinionCardComponent,
  OpinionCardType,
  OpinionNewsletterBoxComponent,
  PromoBlockComponent,
  RecommenderBlockComponent,
  RecommenderBlockType,
  SorozatvetoLayoutComponent,
  SorozatvetoLayoutService,
  VotingComponent,
  WysiwygBoxComponent,
} from 'src/app/shared';
import { MnoBrandingBoxExComponent } from 'src/app/shared/components/branding-box-ex/branding-box-ex.component';
import { FreshNewsAdapterComponent } from 'src/app/shared/components/fresh-news-adapter/fresh-news-adapter.component';
import { WidgetBrandingBoxComponent } from 'src/app/shared/components/microsite-widgets/widget-branding-box/widget-branding-box.component';
import { WidgetKulturnemzetComponent } from 'src/app/shared/components/microsite-widgets/widget-kulturnemzet/widget-kulturnemzet.component';
import { WidgetVisegradComponent } from 'src/app/shared/components/microsite-widgets/widget-visegrad/widget-visegrad.component';
import { PodcastBoxComponent } from 'src/app/shared/components/podcast-box/podcast-box.component';
import { WhereTheBallWillBeComponent } from 'src/app/shared/components/where-the-ball-will-be/where-the-ball-will-be.component';
import { MNO_EXTRACTORS_CONFIG } from '../../layout-extractors/extractor.config';

import { DossierSponsorationHeaderComponent } from '../../../../shared/components/dossier-sponsoration-header/dossier-sponsoration-header.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';
import { SponsoredQuizComponent } from '../../../../shared/components/sponsored-quiz/sponsored-quiz.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    WidgetVisegradComponent,
    KesmaLayoutComponent,
    NgIf,
    BlockTitleRowComponent,
    AdvertisementAdoceanComponent,
    ArticleCardComponent,
    OpinionCardComponent,
    BlogRecommendationComponent,
    WidgetKulturnemzetComponent,
    PromoBlockComponent,
    BayerBlogBlockComponent,
    RealEstateBazaarBlockComponent,
    OpinionNewsletterBoxComponent,
    DossierCardComponent,
    MostReadComponent,
    RecommenderBlockComponent,
    KoponyegComponent,
    VotingComponent,
    ArticlePageImageComponent,
    WysiwygBoxComponent,
    SorozatvetoLayoutComponent,
    NewspaperBoxSidebarComponent,
    RealEstateBazaarSearchBlockComponent,
    FreshNewsAdapterComponent,
    HtmlEmbedComponent,
    AsyncPipe,
    WidgetBrandingBoxComponent,
    MnoBrandingBoxExComponent,
    NgTemplateOutlet,
    PodcastBoxComponent,
    WhereTheBallWillBeComponent,
    SponsoredBoxComponent,
    DossierSponsorationHeaderComponent,
    SponsoredVotingComponent,
    QuizComponent,
    VariableSponsoredDidYouKnowWrapperComponent,
    SponsoredQuizComponent,
  ],
  providers: [provideLayoutDataExtractors(MNO_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent implements OnDestroy, OnInit, OnChanges {
  @Input() adPageType = PAGE_TYPES.main_page;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() showLabels = true;
  @Input() editorFrameSize?: 'desktop' | 'mobile';
  firstBlockTitle: string;

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;
  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;
  readonly RecommenderBlockType = RecommenderBlockType;
  readonly OpinionCardType = OpinionCardType;
  readonly ArticleCardType = ArticleCardType;
  readonly KoponyegType: KoponyegType = KoponyegType.Light;
  readonly EBPortalEnum = EBPortalEnum;
  readonly KoponyegDefinitions: KoponyegDefinitions = {
    width: 300,
    height: 100,
  };
  public realEstateData: RealEstateBazaarData[] = [];
  socialInfo = {
    facebookLink: 'https://www.facebook.com/magyarnemzet.hu',
    instagramLink: 'https://www.instagram.com/magyarnemzet.hu/?hl=en',
    youtubeLink: 'https://www.youtube.com/@magyarnemzet_hivatalos',
    twitterLink: 'https://twitter.com/MagyarNemzetOn',
    videaLink: 'https://videa.hu/csatornak/magyar-nemzet-404',
  };
  opinionInfoLink = opinionNewsletterInfoLink;
  readonly isMobile$: Observable<boolean>;
  readonly DossierCardTypes = DossierCardStyleTypes;
  didYouKnowData$: BehaviorSubject<EbDidYouKnow | undefined> = new BehaviorSubject<EbDidYouKnow | undefined>(undefined);
  isEBEnabled = false;
  readonly isBayerBlogEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS);
  private minuteToMinuteLoaders: Record<string, Observable<MinuteToMinuteBlock[]>> = {};
  private galleryLoaders: Record<string, Observable<GalleryDetails[]>> = {};
  private extractedData: any = {};
  private realEstateDataLoading = false;
  private readonly destroy$ = new Subject<void>();
  private newsFeedLoaders: Record<string, Observable<BasicDossier[]>> = {};

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    public readonly voteService: VoteService,
    public readonly httpClient: HttpClient,
    public readonly sorozatvetoLayoutService: SorozatvetoLayoutService,
    private readonly dossierService: DossierService,
    private readonly configService: ConfigService,
    private readonly articleService: ArticleService,
    private readonly galleryService: GalleryService,
    private readonly analyticsService: AnalyticsService,
    private readonly portalConfigService: PortalConfigService,
    private readonly ebService: EbService,
    private readonly bayerBlogService: BayerBlogService
  ) {
    this.isMobile$ = this.configService.isMobile$;
    this.isEBEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  }

  get bayerZsolt$(): Observable<Author | undefined> {
    return this.bayerBlogService.author$;
  }

  ngOnInit(): void {
    this.firstBlockTitle = this.findFirstElementWithBlockTitle(this.structure);
    this.getDidYouKnowData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['structure'] || changes['configuration']) {
      this.firstBlockTitle = this.findFirstElementWithBlockTitle(this.structure);
      this.clearExtractedData();
    }
  }

  findFirstElementWithBlockTitle(elements: (LayoutElementRow | LayoutElementColumn | LayoutElementContent)[]): string {
    if (!elements?.length) return '';

    return elements.reduce((acc: string, element) => {
      if (acc) return acc;
      if (element?.blockTitle?.text) return element?.id;
      if ((element as LayoutElementRow)?.elements?.length) {
        return this.findFirstElementWithBlockTitle((element as LayoutElementRow).elements) || acc;
      }

      return acc;
    }, '');
  }

  getData(type: keyof LayoutDataExtractor, layoutElement: LayoutElement, extractor: any, index?: number, ...extractorParams: any[]): any {
    const id = type + layoutElement.id + index;
    if (!this.extractedData[id]) {
      this.extractedData[id] = index || index == 0 ? extractor[type]?.(layoutElement, index, ...extractorParams) : extractor[type]?.(layoutElement);
    }

    if (type === 'getNewsFeedData' && this.extractedData[id]?.slug && !this.newsFeedLoaders[this.extractedData[id]?.slug]) {
      const data = this.extractedData[id] as ArticleCard;

      this.newsFeedLoaders[data.slug as string] = this.dossierService
        .getDossier(data.slug as string)
        .pipe(catchError(() => of({ data: [] })))
        .pipe(map(({ data }) => data));

      this.newsFeedLoaders[data.slug as string].pipe(takeUntil(this.destroy$)).subscribe((secondaryArticles) => {
        this.extractedData[id] = { ...data, secondaryArticles };
        this.changeDetector.markForCheck();
      });
    }

    if ((layoutElement as LayoutElementContent).contentType === LayoutElementContentType.MinuteToMinute) {
      this.getMinuteToMinuteData(id);
    }

    if ((layoutElement as LayoutElementContent).contentType === LayoutElementContentType.Gallery) {
      this.getGalleriesData(id);
    }

    return this.extractedData[id];
  }

  getSeveralElementBoxTitle(layoutElement: any): string {
    const selectedDossier = layoutElement?.config?.selectedDossiers[0];
    return selectedDossier?.overwriteTitle || selectedDossier?.original?.title || 'Dosszié';
  }

  handleRealEstateInitEvent(): void {
    if (!this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading = true;
    const realEstates: Array<RealEstateBazaarData> = [];

    this.httpClient
      .get(
        // eslint-disable-next-line max-len
        'https://www.ingatlanbazar.hu/api/property-search?property_location=6,1000000004,1000000005,1000000006,1000000007&amp;;property_newbuildonly=on&amp;property__2=3_2'
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData = realEstates;
        this.realEstateDataLoading = false;
        this.changeDetector.detectChanges();
      });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.changeDetector.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  clearExtractedData(): void {
    this.extractedData = {};
  }

  onSubscribe(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }

  asArticleImage(data: ImageLayoutData): Article & { url: string } {
    return {
      thumbnail: data.fullSize,
      thumbnailInfo: {
        altText: data.altText,
        source: data.source,
        photographer: data.photographer,
        caption: data.caption,
        title: data.caption,
      },
      url: data.url,
    } as Article & { url: string };
  }

  getDidYouKnowData(): void {
    const didYouKnowArray: EbDidYouKnow[] = this.ebService.didYouKnowData;
    if (!didYouKnowArray?.length) {
      this.didYouKnowData$.next(undefined);
      return;
    }
    const randomIndex = Math.floor(Math.random() * didYouKnowArray?.length);
    this.didYouKnowData$.next({ ...didYouKnowArray[randomIndex] });
  }

  private getMinuteToMinuteData(dataId: string): void {
    const article: ArticleCard = this.extractedData[dataId];
    const slug = article?.slug ?? '';

    if (slug && !this.minuteToMinuteLoaders[slug]) {
      const { publishYear, publishMonth, columnSlug } = article;
      this.minuteToMinuteLoaders[slug] = this.articleService.getArticle(columnSlug ?? '', publishYear as string, publishMonth as string, slug ?? '').pipe(
        catchError(() => []),
        map(({ data }) => data?.minuteToMinuteBlocks ?? [])
      );

      this.minuteToMinuteLoaders[slug].pipe(takeUntil(this.destroy$)).subscribe((minuteToMinuteBlocks) => {
        (this.extractedData[dataId] as Article) = {
          ...article,
          minuteToMinuteBlocks: minuteToMinuteBlocks.slice(0, 2),
        } as any;
        this.changeDetector.detectChanges();
      });
    }
  }

  private getGalleriesData(dataId: string): void {
    const galleries: GalleryData[] = this.extractedData[dataId] ?? [];
    const slugs = galleries.filter((g) => !!g).map(({ slug }) => slug);

    if (slugs?.length && !this.galleryLoaders[dataId]) {
      this.galleryLoaders[dataId] = forkJoin(slugs.map((slug) => this.galleryService.getGalleryDetails(slug).pipe(catchError(() => []))));

      this.galleryLoaders[dataId].pipe(takeUntil(this.destroy$)).subscribe((galleryDetails) => {
        this.extractedData[dataId] = galleries.map((gallery, i) => ({
          ...gallery,
          ...galleryDetails,
          isAdultsOnly: gallery?.isAdult,
          count: galleryDetails[i]?.images?.length,
        }));
        this.changeDetector.detectChanges();
      });
    }
  }
}
