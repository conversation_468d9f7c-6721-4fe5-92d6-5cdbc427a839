import { inject, Injectable, signal, WritableSignal } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResult, BackendWeatherData, BackendWeatherForecast, CityWeatherCurrent, WeatherCity, WeatherForecast } from '@trendency/kesma-ui';
import { MNOWeatherData } from './weather.definitions';
import { backendWeatherForecastToWeatherForecast, translateWeatherIcon } from './weather.utils';

@Injectable()
export class WeatherService {
  private readonly reqService = inject(ReqService);
  selectedCity: WritableSignal<WeatherCity> = signal('Székesfehérvár');

  getWeather(): Observable<MNOWeatherData> {
    return this.reqService.get<ApiResult<BackendWeatherData>>('/mediaworks/weather').pipe(map(({ data }) => this.mapBackendWeatherToWeather(data)));
  }

  getWeatherByCity(city?: WeatherCity): Observable<CityWeatherCurrent | undefined> {
    return this.getWeather().pipe(map((weather) => weather.current.filter((item) => item.city === (city ?? this.selectedCity())).shift()));
  }

  private readonly mapBackendWeatherToWeather = (backendWeatherData: BackendWeatherData): MNOWeatherData => ({
    ...backendWeatherData,
    current: this.mapBackendCurrentWeather(backendWeatherData.current),
    forecast: this.mapBackendForecast(backendWeatherData.forecast),
  });

  private readonly mapBackendCurrentWeather = (currentWeather: CityWeatherCurrent[]): CityWeatherCurrent[] =>
    currentWeather.map((weather) => ({
      ...weather,
      icon2: translateWeatherIcon(weather.icon2),
    }));

  private readonly mapBackendForecast = (forecast: Record<WeatherCity, BackendWeatherForecast[]>): Record<WeatherCity, WeatherForecast[]> => {
    return Object.fromEntries(Object.entries(forecast).map(([city, forecasts]) => [city, forecasts.map(backendWeatherForecastToWeatherForecast)])) as Record<
      WeatherCity,
      WeatherForecast[]
    >;
  };
}
