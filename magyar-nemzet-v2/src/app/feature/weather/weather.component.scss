@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  margin-top: 32px;
}

.wrapper.with-aside {
  margin-top: 20px;
  @include media-breakpoint-up(md) {
    margin-top: 32px;
  }
}

.left-column {
  .title {
    color: var(--kui-blue-900);
    margin-bottom: 24px;

    @include media-breakpoint-up(md) {
      margin-bottom: 32px;
    }
  }

  .national {
    margin-bottom: 32px;
  }

  .forecast {
    margin-bottom: 47px;

    @include media-breakpoint-up(md) {
      margin-bottom: 48px;
    }

    &::ng-deep {
      .item-container {
        gap: 16px;
        .item {
          max-width: 156px;
        }
      }

      .bottom-navigation {
        margin-top: 24px;

        @include media-breakpoint-up(md) {
          margin-top: 32px;
        }
      }
    }

    &-box {
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: 156px;

      &-top {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 16px;
        align-items: center;
        border-radius: 4px 4px 0 0;
        background-color: var(--kui-blue-100);

        &-day {
          color: var(--kui-vlue-800);
          font-size: 20px;
          font-weight: 500;
          letter-spacing: 0.2px;
          text-transform: capitalize;
          line-height: normal;
        }

        &-date {
          color: var(--kui-blue-500);
          font-size: 14px;
          font-weight: 500;
          letter-spacing: 0.14px;
          text-transform: capitalize;
          line-height: normal;
        }
      }

      &-item {
        display: flex;
        gap: 16px;
        padding: 16px;
        border: 1px solid var(--kui-blue-50);
        font-size: 20px;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: 0.2px;
        color: var(--kui-blue-900);

        &.wide {
          padding: 16px 8px;
        }

        &.bg {
          background-color: var(--kui-blue-50);
        }

        &.last-item {
          border-radius: 0 0 4px 4px;
        }

        .avg {
          font-size: 40px;
          font-weight: 500;
          letter-spacing: 0.4px;
          display: flex;
          align-items: center;
        }

        .max,
        .min {
          white-space: nowrap;

          kesma-icon {
            display: inline-block;
            margin-left: -6px;
          }
        }

        .max {
          color: var(--kui-red-500);
        }

        .min {
          color: var(--kui-blue-500);
        }

        .bottom-text {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .forecast-national {
    margin-bottom: 141px;

    @include media-breakpoint-up(md) {
      margin-bottom: 48px;
    }

    .day {
      color: var(--kui-blue-900);
      font-size: 20px;
      line-height: 24px;
      margin-bottom: 8px;
      font-weight: 400;
    }

    .text {
      font-size: 18px;
      line-height: 24px;
      margin-bottom: 32px;
    }
  }

  .recommendation {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 42px;

    @include media-breakpoint-up(md) {
      margin-bottom: 24px;
    }

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-title {
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        letter-spacing: 0.16px;
      }

      &-link {
        display: none;
        color: var(--kui-slate-950);
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        white-space: nowrap;

        @include media-breakpoint-up(md) {
          display: block;
        }

        kesma-icon {
          display: inline-block;
        }
      }
    }

    &-link-mobile {
      display: block;
      width: 100%;
      color: var(--kui-slate-950);
      white-space: nowrap;
      text-align: center;
      padding: 6px 8px;
      border-radius: 2px;
      border: 1px solid var(--kui-slate-950);
      margin: -16px 0 42px;

      @include media-breakpoint-up(md) {
        display: none;
      }

      kesma-icon {
        display: inline-block;
      }
    }

    &-articles {
      display: flex;
      flex-direction: column;
      gap: 16px;
      flex-wrap: wrap;

      @include media-breakpoint-down(md) {
        article.article-card {
          border-right: 0.5px solid var(--kui-slate-950);
          border-bottom: 0.5px solid var(--kui-slate-950);
          padding: 0 8px 8px 0;

          &::ng-deep .article {
            &-title {
              // These are defined as !important in the article-card:
              font-size: 14px !important;
              font-weight: 700 !important;
              line-height: 18px !important;
              letter-spacing: 0.14px !important;
            }

            &-lead {
              &-text {
                display: none;
              }
            }

            &-thumbnail {
              width: 96px;
              height: 96px;

              &-figure {
                margin: auto;
              }
            }
          }
        }
      }

      @include media-breakpoint-up(md) {
        flex-direction: row;

        article.article-card {
          max-width: 155px;
          margin: 0 auto;
        }
      }
    }
  }
}

.forecast::ng-deep {
  .navigation-button {
    &.disabled .navigation {
      color: var(--kui-blue-200);
    }

    .navigation {
      transform: rotateZ(90deg);
      color: var(--kui-blue-900);

      &.left {
        transform: rotate(-90deg);
      }
    }
  }

  .pagination-dot {
    .bullet:not(.active) {
      margin: 0 16px;
      color: var(--kui-blue-300, #89c8f0);
    }

    &.active .bullet {
      color: var(--kui-blue-900);
    }
  }
}

mno-promo-block {
  margin-bottom: 32px;

  @include media-breakpoint-up(md) {
    margin-bottom: 24px;
  }
}

kesma-advertisement-adocean {
  display: block;
  margin: 41px auto 32px;

  @include media-breakpoint-up(md) {
    margin: 24px auto;
  }
}
