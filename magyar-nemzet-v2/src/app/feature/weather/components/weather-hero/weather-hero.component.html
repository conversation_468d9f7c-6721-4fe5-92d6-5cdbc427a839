<div class="header">
  <div class="header-title">
    <h1 class="header-title-text">I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
    <div class="header-title-line"></div>
  </div>
  <span class="header-source"> <PERSON>z adatok szolgáltatója: <a href="https://koponyeg.hu/" target="_blank">köpönyeg.hu</a> </span>
</div>

<div class="content">
  <div class="content-panel">
    <div class="content-panel-input">
      <span class="content-panel-input-label">Település kiválasztása</span>
      <ng-select [(ngModel)]="selectedCity" [items]="selectableCities()" [clearable]="false" />
    </div>
    <div class="content-panel-data">
      @if (selectedWeather()?.current; as current) {
        <div class="content-panel-data-skyview">
          <p class="content-panel-data-skyview-text">{{ current.skyView }}</p>
          <div class="content-panel-data-skyview-sun">
            <kesma-icon name="weather/sunset2" [size]="32" />
            <span class="content-panel-data-skyview-sun-time">
              {{ current.sunrise | slice: 0 : -3 }}
              <kesma-icon name="arrow-up" [size]="20" />
            </span>
            <span class="content-panel-data-skyview-sun-time">
              {{ current.sunset | slice: 0 : -3 }}
              <kesma-icon name="arrow-down" [size]="20" />
            </span>
          </div>
        </div>
        <div class="content-panel-data-temp">
          <div class="content-panel-data-temp-current">
            <kesma-icon [name]="'weather/' + current.icon2" [size]="88" />
            <span class="content-panel-data-temp-current-text">{{ current.temperature }}°</span>
          </div>
          <div class="content-panel-data-temp-minmax">
            <kesma-icon name="weather/sunrise2" [size]="32" />
            <span class="content-panel-data-temp-minmax-max">
              {{ current.maxTemperature }}°
              <kesma-icon name="arrow-up" [size]="20" />
            </span>
            <span class="content-panel-data-temp-minmax-min">
              {{ current.minTemperature }}°
              <kesma-icon name="arrow-down" [size]="20" />
            </span>
          </div>
        </div>
        <div class="content-panel-data-misc">
          <div class="content-panel-data-misc-item">
            <span class="content-panel-data-misc-item-label">Csapadék</span>
            <span class="content-panel-data-misc-item-value">{{ current.rain }} mm</span>
          </div>
          <div class="content-panel-data-misc-item">
            <span class="content-panel-data-misc-item-label">Légnyomás</span>
            <span class="content-panel-data-misc-item-value">{{ current.airPressure }} hPa</span>
          </div>
          <div class="content-panel-data-misc-item">
            <span class="content-panel-data-misc-item-label">Szélerősség</span>
            <span class="content-panel-data-misc-item-value">{{ current.wind }} km/h</span>
          </div>
          <div class="content-panel-data-misc-item">
            <span class="content-panel-data-misc-item-label">Szélirány</span>
            <span class="content-panel-data-misc-item-value">{{ current.windDirection }}</span>
          </div>
          <div class="content-panel-data-misc-item">
            <span class="content-panel-data-misc-item-label">UV</span>
            <span class="content-panel-data-misc-item-value uv">{{ selectedWeather().uv.text }}</span>
          </div>
        </div>
      }
    </div>
  </div>
  <div class="content-map">
    <div class="content-map-container">
      <img src="/assets/images/icons/weather/map.svg" alt="Időjárás térkép" loading="eager" />
      @for (city of mapCities(); track city.city) {
        <div class="content-map-layer" [class]="city.city" [title]="city.city">
          <div class="content-map-layer-box">
            <kesma-icon [name]="'weather/' + city.icon2" [size]="22" />
            <div class="content-map-layer-divider"></div>
            <div class="content-map-layer-text">
              <p class="content-map-layer-max">{{ city.maxTemperature }}°</p>
              <p class="content-map-layer-min">{{ city.minTemperature }}°</p>
            </div>
          </div>
          <p class="content-map-city">{{ city.city }}</p>
        </div>
      }
    </div>
  </div>
</div>
