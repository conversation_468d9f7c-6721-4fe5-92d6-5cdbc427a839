import { ChangeDetectionStrategy, Component, input, model } from '@angular/core';
import { CityWeatherCurrent, IconComponent, WeatherCity, WeatherData } from '@trendency/kesma-ui';
import { NgSelectComponent } from '@ng-select/ng-select';
import { SlicePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-weather-hero',
  imports: [NgSelectComponent, IconComponent, SlicePipe, FormsModule],
  templateUrl: './weather-hero.component.html',
  styleUrl: './weather-hero.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WeatherHeroComponent {
  readonly selectedCity = model.required<WeatherCity>();
  readonly selectedWeather = input.required<WeatherData>();
  readonly mapCities = input.required<CityWeatherCurrent[]>();
  readonly selectableCities = input.required<WeatherCity[]>();
}
