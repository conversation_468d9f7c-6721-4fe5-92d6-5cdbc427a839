<section class="hero">
  <app-weather-hero [(selectedCity)]="selectedCity" [selectableCities]="selectableCities()" [selectedWeather]="selectedWeather()" [mapCities]="mapCities()" />
</section>

<section class="wrapper with-aside">
  <div class="left-column">
    <h2 class="title">9 napos előrejelzés</h2>
    <ng-template #previousNavigation>
      <kesma-icon class="navigation left" name="arrow-up" [size]="24" />
    </ng-template>
    <ng-template #nextNavigation>
      <kesma-icon class="navigation" name="arrow-up" [size]="24" />
    </ng-template>
    <ng-template #bulletTemplate>
      <kesma-icon class="bullet" name="bullet" [size]="10" />
    </ng-template>
    @if (selectedWeather()?.forecast) {
      <div
        class="forecast"
        kesma-swipe
        [data]="selectedWeather().forecast | slice: 0 : 9"
        [itemTemplate]="forecastTemplate"
        [previousNavigationTemplate]="previousNavigation"
        [nextNavigationTemplate]="nextNavigation"
        [bulletTemplate]="bulletTemplate"
        [useNavigation]="true"
        [usePagination]="true"
        dataTrackByProperty="day"
        [breakpoints]="breakpoints"
      ></div>
    }
    <h2 class="title national">Országos előrejelzés</h2>
    <div class="forecast-national">
      @for (data of selectedWeather().text; track data.day) {
        <h3 class="day">{{ data.day }}</h3>
        <div class="text">{{ data.text }}</div>
      }
    </div>

    @if (weatherArticles()?.data?.length) {
      <div class="recommendation">
        <div class="recommendation-header">
          <h2 class="recommendation-header-title">Az időjárás legfrissebb hírei</h2>
          <a class="recommendation-header-link" [routerLink]="['/', 'rovat', 'idojaras']">
            Tovább az összes cikkhez
            <kesma-icon name="chevron-right-dark" [size]="14" [height]="16" />
          </a>
        </div>
        <div class="recommendation-articles">
          @for (article of weatherArticles()?.data; track article.id) {
            <article [data]="article" mno-article-card [styleID]="isMobile() ? recommendedArticleStyleMobile : recommendedArticleStyle"></article>
          }
        </div>
        <a class="recommendation-link-mobile" [routerLink]="['/', 'rovat', 'idojaras']">
          Tovább az összes cikkhez
          <kesma-icon name="chevron-right-dark" [size]="14" [height]="16" />
        </a>
      </div>
    }

    <mno-promo-block
      (subscriptionClick)="onSubscribe()"
      [isWide]="true"
      [facebookLink]="socialInfo.facebookLink"
      [instagramLink]="socialInfo.instagramLink"
      [twitterLink]="socialInfo.twitterLink"
      [videaLink]="socialInfo.videaLink"
      [youtubeLink]="socialInfo.youtubeLink"
    />

    <app-external-recommendations
      [roadblock_ottboxextra]="adverts()?.desktop?.['roadblock_ottboxextra']"
      [mobilrectangle_ottboxextra]="adverts()?.mobile?.['mobilrectangle_ottboxextra']"
    />

    @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
      <kesma-advertisement-adocean
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      />
    }

    @if (adverts()?.desktop?.roadblock_1; as ad) {
      <kesma-advertisement-adocean
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      />
    }
  </div>
  <aside>
    <app-sidebar />
  </aside>
</section>

<ng-template #forecastTemplate let-data="data">
  <div class="forecast-box">
    <div class="forecast-box-top">
      <div class="forecast-box-top-day">
        {{ data.date | dfnsFormat: 'EEEE' }}
      </div>
      <div class="forecast-box-top-date">
        {{ data.date | dfnsFormat: 'MMMM d.' }}
      </div>
    </div>
    <div class="forecast-box-item wide">
      <kesma-icon [name]="'weather/' + data.icon2" title="data.description" [size]="48" />
      <span class="avg">{{ (data.maxTemperature + data.minTemperature) / 2 | number: '1.0-0' }}°</span>
    </div>
    <div class="forecast-box-item">
      <span class="max">
        {{ data.maxTemperature }}°
        <kesma-icon name="arrow-up" [size]="20" />
      </span>
      <span class="min">
        {{ data.minTemperature }}°
        <kesma-icon name="arrow-down" [size]="20" />
      </span>
    </div>
    <div class="forecast-box-item bg">
      <kesma-icon name="weather/rain" [size]="32" />
      <p class="bottom-text">{{ data.rain }} mm</p>
    </div>
    <div class="forecast-box-item bg last-item">
      <kesma-icon name="weather/wind" [size]="32" />
      <p class="bottom-text">{{ data.wind }} km/h</p>
    </div>
  </div>
</ng-template>
