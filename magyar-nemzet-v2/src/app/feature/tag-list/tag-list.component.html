<section class="wrapper flex-column">
  <app-eb-header-logo *ngIf="isEBEnabled"></app-eb-header-logo>
  <mno-block-title-row [data]="titleRow" [isFullWidth]="true" [headingLevel]="1"></mno-block-title-row>

  <p *ngIf="rowAllCount === 0; else firstResult" class="tags-notfound text-center">nincs találat</p>

  <ng-template #firstResult>
    <article
      mno-article-card
      [data]="articles?.[0]"
      [styleID]="(isMobile$ | async) ? ArticleCardType.Img16TopTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWideLarge"
      [showThumbnail]="!!articles?.[0]?.thumbnail"
      [asResult]="(isMobile$ | async) === true"
      [imageLazyLoad]="false"
    ></article>
  </ng-template>

  <div class="tags-ad" *ngIf="ads?.desktop?.roadblock_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>

  <div class="tags-ad-sm" *ngIf="ads?.mobile?.mobilrectangle_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</section>

<div class="wrapper with-aside tags-list-container">
  <div class="tags-list d-flex flex-column left-column">
    <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
    <ng-container *ngFor="let article of articles | slice: 1; let i = index; let isLast = last; trackBy: trackByFn">
      <article
        mno-article-card
        [data]="article"
        [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
        [showThumbnail]="!!article?.thumbnailUrl || !!article?.thumbnailUrl43 || !!article?.thumbnail?.url"
        [asResult]="(isMobile$ | async) === true"
        [imageLazyLoad]="i >= 7"
      ></article>

      <hr class="list-separator" *ngIf="!isLast" />

      <ng-container *ngIf="i === 3">
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      </ng-container>
    </ng-container>

    <mno-pager
      *ngIf="rowAllCount > 0"
      [rowAllCount]="rowAllCount"
      [rowOnPageCount]="rowOnPageCount"
      [isListPager]="true"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="5"
    ></mno-pager>

    <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>
  <app-sidebar></app-sidebar>
</div>
