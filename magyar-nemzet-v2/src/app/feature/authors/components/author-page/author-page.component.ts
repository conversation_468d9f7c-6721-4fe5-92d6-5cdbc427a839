import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  ArticlesByDate,
  backendDateToDate,
  getStructuredDataForProfilePage,
  LimitableMeta,
  PortalConfigSetting,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { BackendAuthorData, ConfigService, defaultMetaInfo, PortalConfigService } from '../../../../shared';
import { AsyncPipe, NgClass, NgForOf, NgIf } from '@angular/common';
import { ArticleCardComponent, ArticleCardType, PagerComponent } from 'src/app/shared';
import { SearchFilterComponent } from 'src/app/shared/components/search-filter/search-filter.component';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-author-page',
  templateUrl: './author-page.component.html',
  styleUrls: ['./author-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AdvertisementAdoceanComponent, NgIf, ArticleCardComponent, PagerComponent, AsyncPipe, SearchFilterComponent, NgClass, NgForOf],
})
export class AuthorPageComponent implements OnInit {
  author: BackendAuthorData;
  firstArticleDate: string | undefined;
  articlesByDate: ArticlesByDate[] = [];
  limitable?: LimitableMeta;

  PortalConfigSetting = PortalConfigSetting;
  ArticleCardType = ArticleCardType;

  adverts?: AdvertisementsByMedium;

  articles: ArticleCard[] = [];

  isMobile$: Observable<boolean>;

  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef,
    public readonly portalConfigService: PortalConfigService,
    private readonly configService: ConfigService,
    private readonly schemaService: SchemaOrgService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.data
      .pipe(
        map(({ data }) => data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(({ author, articles, limitable }) => {
        this.author = author as BackendAuthorData;
        this.firstArticleDate = articles?.length
          ? format(backendDateToDate(articles[0].publishDate as string) ?? new Date(), 'yyyy. MMMM', { locale: hu })
          : undefined;
        this.articles = articles;
        this.articlesByDate = this.groupDataByDate(articles.slice(1));
        this.limitable = limitable;

        this.schemaService.removeStructuredData();
        this.schemaService.insertSchema(getStructuredDataForProfilePage(this.author as any, environment?.siteUrl ?? ''));

        this.setMetaData();
        this.initAds();
        this.cdr.markForCheck();
      });
  }

  onSearch(filters: Record<string, string | undefined>): void {
    // Remove empty props
    Object.entries(filters).forEach(([key, value]) => {
      if (!value) {
        delete filters[key];
      }
      if (!filters['from_date'] && filters['to_date']) {
        delete filters['to_date'];
      }

      delete filters['author'];
    });

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...filters },
    });
  }

  private setMetaData(): void {
    const title = `${this.author.publicAuthorName} | Magyar Nemzet`;
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description: `${this.author.publicAuthorName} szerzőnk legfrissebb cikkei a Magyar Nemzet oldalán - Szellemi honvédelem 1938 óta.`,
      ogDescription: `${this.author.publicAuthorName} szerzőnk legfrissebb cikkei a Magyar Nemzet oldalán - Szellemi honvédelem 1938 óta.`,
    });

    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical, { addHostUrl: true });
  }

  private initAds(): void {
    this.adverts = undefined;
    this.cdr.markForCheck();

    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.markForCheck();
    });
  }

  private groupDataByDate(competitions: ArticleCard[]): ArticlesByDate[] {
    return competitions.reduce<ArticlesByDate[]>((acc, item) => {
      const date = format(backendDateToDate(item.publishDate as string) ?? new Date(), 'yyyy. MMMM', { locale: hu });
      let group = acc.find((g) => {
        return g.date === date;
      });
      if (!group) {
        group = { date, articles: [] };
        acc.push(group);
      }
      group?.articles.push(item);
      return acc;
    }, []);
  }
}
