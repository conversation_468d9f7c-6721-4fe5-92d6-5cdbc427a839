<ng-container *ngIf="!isUserAdultChoice && isAdultsOnly; else adultContent" class="article-page">
  <app-adult (isUserAdult)="onIsUserAdultChoose($event)"></app-adult>
</ng-container>

<ng-template #adultContent>
  <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_1 as ad" [ad]="ad" class="fullwidth-ad-zone"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_1 as ad" [ad]="ad" class="fullwidth-ad-zone"></kesma-advertisement-adocean>

  <section *ngIf="dossier?.slug" class="block article-block">
    <div class="wrapper">
      <div class="article d-flex flex-column">
        <div class="newsfeed-top">
          <img [size]="24" alt="Hírfolyam ikon" mno-icon="news" /> Hírfolyam
          <span class="newsfeed-top-date">{{ mainArticle?.publishDate | dfnsFormat: 'Pp' }}</span>
        </div>
        <app-article-header [article]="mainArticle!" [languages]="[]" [url]="url"></app-article-header>

        <ng-container *ngFor="let article of articles; let index = index">
          <div class="newsfeed-article-container">
            <div class="newsfeed-article-header">
              <mno-opinion-author [data]="getAuthor(article)" size="small"></mno-opinion-author>
              <div class="newsfeed-article-meta">
                <mno-tag-list [data]="article?.tags"></mno-tag-list>
                <span class="newsfeed-article-meta-date">{{ article.publishDate | publishDate }}</span>
              </div>
            </div>

            <h1 class="article-title">{{ article?.title }}</h1>
            <div class="article-lead">{{ article?.lead ?? article?.excerpt }}</div>
            <mno-article-page-image [data]="article"></mno-article-page-image>
            <ng-container [ngTemplateOutletContext]="{ article }" [ngTemplateOutlet]="bodyContent"></ng-container>
            <mno-social-share [isMobile]="isMobile$ | async" [link]="[url]" [title]="article?.title" color="dark"></mno-social-share>
          </div>
          <ng-container *ngIf="index === 0">
            <mno-promo-block (subscriptionClick)="onSubscriptionClick()" [isWide]="true"></mno-promo-block>
          </ng-container>
          <ng-container *ngIf="index === 1">
            <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
            <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
          </ng-container>
          <ng-container *ngIf="index === 3">
            <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
            <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
          </ng-container>
        </ng-container>

        <ng-container *ngTemplateOutlet="rovatAjanlo"></ng-container>
        <mno-promo-block [isWide]="true"></mno-promo-block>
        <app-external-recommendations
          [roadblock_ottboxextra]="ads?.desktop?.['roadblock_ottboxextra']"
          [mobilrectangle_ottboxextra]="ads?.mobile?.['mobilrectangle_ottboxextra']"
        ></app-external-recommendations>
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <ng-container *ngTemplateOutlet="cimoldalrolAjanljuk"></ng-container>
      </div>
      <div class="sidebar">
        <app-sidebar [articleId]="mainArticle?.id ?? ''" [articleSlug]="mainArticle?.slug ?? ''" [categorySlug]="mainArticle?.columnSlug ?? ''"></app-sidebar>
      </div>
    </div>
  </section>
  <app-newsletter-popup></app-newsletter-popup>
</ng-template>

<ng-template #bodyContent let-article="article">
  <ng-container *ngFor="let element of article?.body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <mno-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></mno-wysiwyg-box>
        </ng-container>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.Voting" class="voting-block"></div>

      <div *ngSwitchCase="ArticleBodyType.MediaVideo" class="block-video">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Gallery" class="block-gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id] as gallery">
          <mno-gallery-card [isInsideAdultArticleBody]="isAdultsOnly" [data]="gallery"></mno-gallery-card>
        </ng-container>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Advert">
        <kesma-advertisement-adocean
          *ngIf="ads?.desktop?.roadblock_1 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="ads?.mobile?.mobilrectangle_1 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>
      </div>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #cimoldalrolAjanljuk>
  <app-recommendation-block
    *ngIf="recommendedArticles?.length"
    [articles]="recommendedArticles"
    [desktopStyle]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder"
    [isMobile]="isMobile$ | async"
    [mobileStyle]="ArticleCardType.ImgRightTagsTitleLeadWideBorder"
    [title]="lowPriorityRecommendationTitle"
  >
  </app-recommendation-block>

  <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
</ng-template>

<ng-template #rovatAjanlo>
  <app-recommendation-block
    *ngIf="recommendedHighPriorityArticles?.length"
    [articles]="recommendedHighPriorityArticles"
    [desktopStyle]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder"
    [isMobile]="isMobile$ | async"
    [mobileStyle]="ArticleCardType.ImgRightTagsTitleLeadWideBorder"
    [title]="highPriorityRecommendationTitle"
  >
  </app-recommendation-block>
</ng-template>
