import { Async<PERSON>ipe, DOCUMENT, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PublishDatePipe, SeoService, StorageService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  Article,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleVideoComponent,
  Author,
  backendDateToDate,
  BlockTitle,
  buildArticleUrl,
  createCanonicalUrlForPageablePage,
  GalleryData,
  GalleryElementData,
  NEWSLETTER_COMPONENT_TYPE,
  PAGE_TYPES,
} from '@trendency/kesma-ui';
import { combineLatest, forkJoin, map, Observable, of, Subject, switchMap, takeUntil } from 'rxjs';
import { GalleryService } from 'src/app/shared/services/gallery.service';
import { ArticleService, ConfigService, defaultMetaInfo, ExternalRecommendationsComponent } from '../../../../shared';
import { NewsFeedDossier, NewsFeedRouteData } from '../../news-feed.definitions';
import { RecommendationBlockComponent } from 'src/app/shared/components/recommendation-block/recommendation-block.component';
import {
  ArticleCardType,
  ArticlePageImageComponent,
  GalleryCardComponent,
  IconComponent,
  OpinionAuthorComponent,
  PromoBlockComponent,
  SocialShareComponent,
  TagListComponent,
  WysiwygBoxComponent,
} from 'src/app/shared';
import { ArticleHeaderComponent } from 'src/app/feature/article/components/article-header/article-header.component';
import { ArticleAdultComponent } from 'src/app/feature/article/components/article-adult/article-adult.component';
import { SidebarComponent } from 'src/app/feature/layout/components/sidebar/sidebar.component';
import { NewsletterPopupComponent } from 'src/app/shared/components/newsletter-popup/newsletter-popup.component';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-news-feed',
  templateUrl: './news-feed.component.html',
  styleUrls: ['./news-feed.component.scss'],
  imports: [
    AdvertisementAdoceanComponent,
    AsyncPipe,
    NgIf,
    NgFor,
    NgTemplateOutlet,
    RecommendationBlockComponent,
    WysiwygBoxComponent,
    ArticleVideoComponent,
    GalleryCardComponent,
    PublishDatePipe,
    ArticlePageImageComponent,
    ArticleHeaderComponent,
    OpinionAuthorComponent,
    IconComponent,
    ArticleAdultComponent,
    TagListComponent,
    SocialShareComponent,
    PromoBlockComponent,
    SidebarComponent,
    NewsletterPopupComponent,
    NgSwitch,
    NgSwitchCase,
    ArticleFileLinkDirective,
    DateFnsModule,
    ExternalRecommendationsComponent,
  ],
})
export class NewsFeedComponent implements OnInit, OnDestroy {
  articles: Article[] = [];
  newsfeedSlug: string;
  galleries: Record<string, GalleryData> = {};
  isAdultsOnly = false;
  recommendedHighPriorityArticles: ArticleCard[] = [];
  recommendedArticles: ArticleCard[] = [];
  recommendedLowPriorityArticles: ArticleCard[] = [];
  dossier?: NewsFeedDossier;
  mainArticle?: ArticleCard & Article;
  isUserAdultChoice: boolean;
  rowAllCount = 0;
  page = 0;
  url = '';

  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;

  readonly ArticleCardType = ArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;
  author: Author;
  isMobile$: Observable<boolean>;
  highPriorityRecommendationTitle: BlockTitle;
  lowPriorityRecommendationTitle: BlockTitle;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly articleService: ArticleService,
    private readonly galleryService: GalleryService,
    private readonly cdr: ChangeDetectorRef,
    private readonly storageService: StorageService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly configService: ConfigService,
    private readonly analyticsService: AnalyticsService,
    private readonly seo: SeoService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.params.subscribe((params) => {
      this.newsfeedSlug = params['newsfeedSlug'];
      this.url = this.document?.location?.pathname;
    });
    (this.route.data as Observable<NewsFeedRouteData>).subscribe(
      ({
        data: {
          result,
          recommendations: { data: recommendationsData },
        },
      }) => {
        this.articles = result?.data?.map(this.mapArticleBody.bind(this));
        this.isAdultsOnly = this.articles?.some((article) => article?.isAdultsOnly);
        this.dossier = result?.meta?.newsFeed;

        const firstArticle = this.articles?.[0] ?? {};
        this.mainArticle = {
          id: firstArticle.id,
          title: this.dossier?.newsFeedTitle ?? this.dossier?.title,
          lead: this.dossier?.newsFeedDescription ?? this.dossier?.description,
          slug: this.dossier?.slug,
          thumbnail: this.dossier?.coverImage?.thumbnail ?? firstArticle?.thumbnail ?? '',
          thumbnailInfo: {
            ...this.dossier?.coverImage,
          },
          tags: [...(firstArticle?.tags ?? [])],
          publishDate: backendDateToDate(this.dossier?.createdAt) ?? firstArticle.publishDate,
          publicAuthor: firstArticle.publicAuthor,
          publicAuthorSlug: firstArticle.publicAuthorSlug,
          publicAuthorTitle: firstArticle.publicAuthorTitle,
          columnTitle: firstArticle.columnTitle,
          columnSlug: firstArticle.columnSlug,
        } as ArticleCard & Article;

        this.rowAllCount = result?.meta?.limitable?.rowAllCount as number;

        const { highPriorityArticles, lowPriorityArticles } = recommendationsData;
        this.recommendedHighPriorityArticles = highPriorityArticles ?? [];
        this.recommendedLowPriorityArticles = lowPriorityArticles ?? [];
        this.recommendedArticles = this.recommendedHighPriorityArticles.concat(this.recommendedLowPriorityArticles);
        if (this.recommendedArticles.length > 6) {
          this.recommendedArticles = this.recommendedArticles.slice(0, 6);
        }
        this.highPriorityRecommendationTitle = {
          text: `További ${this.mainArticle.columnTitle} hírek`,
          url: `/rovat/${this.mainArticle.columnSlug}`,
          urlName: 'Tovább az összes cikkhez',
        };
        this.lowPriorityRecommendationTitle = {
          text: 'Címoldalról ajánljuk',
          url: '/',
          urlName: 'Tovább az összes cikkhez',
        };

        this.adStoreAdo.setIsAdultPage(this.isUserAdultChoice);
        this.isUserAdultChoice = (this.storageService.getSessionStorageData('isAdultChoice', false) ?? false) && this.isAdultsOnly;
        this.author = {
          avatar: this.mainArticle.avatar,
          name: this.mainArticle.publicAuthor,
          slug: this.mainArticle.publicAuthorSlug,
        } as Author;
        this.setMetaData();
        this.cdr.markForCheck();
      }
    );

    combineLatest([this.route.data as Observable<NewsFeedRouteData>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(takeUntil(this.unsubscribe$))
      .pipe(
        map<[NewsFeedRouteData, boolean], boolean | undefined>(
          ([
            {
              data: {
                result: {
                  data: [article],
                },
              },
            },
          ]) => article?.withoutAds
        ),
        switchMap((withoutAds) => {
          withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds();
          return withoutAds ? of([]) : this.adStoreAdo.advertisemenets$;
        })
      )
      .subscribe((adsCollection): void => {
        this.ads = this.adStoreAdo.separateAdsByMedium(adsCollection, this.adPageType);

        this.adStoreAdo.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.adStoreAdo.onArticleDestroy();
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adStoreAdo.setIsAdultPage(isUserAdult);
  }

  stringToDate(date: string | Date | undefined): Date | null {
    return backendDateToDate(String(date));
  }

  getDossierLink(isNewsFeed = false): string[] {
    const pageType = isNewsFeed ? 'hirfolyam' : 'dosszie';
    return ['/', pageType, this.dossier?.slug as string];
  }

  getArticleLink(article: Article): string[] {
    return buildArticleUrl(article);
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }

  getAuthor(article: Article): Author {
    return {
      slug: article.publicAuthorSlug,
      avatar: article.avatar,
      name: article.publicAuthor,
      avatarUrl: article.avatar,
    } as Author;
  }

  private mapArticleBody(article: Article): Article {
    this.loadEmbeddedGalleries(article);
    return {
      ...article,
      body: this.articleService.prepareArticleBody(article?.body),
      excerpt: article?.lead || article?.excerpt,
    };
  }

  private loadEmbeddedGalleries(article: Article): void {
    const bodyElements = article?.body ?? [];

    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
          this.cdr.markForCheck();
        });
      });
  }

  private setMetaData(): void {
    const title = `Hírfolyam | Magyar Nemzet`;
    const canonical = createCanonicalUrlForPageablePage('hirfolyam', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical, { addHostUrl: true });
    this.seo.setMetaData({
      ...defaultMetaInfo,
      ogTitle: title,
      title,
    });
  }
}
