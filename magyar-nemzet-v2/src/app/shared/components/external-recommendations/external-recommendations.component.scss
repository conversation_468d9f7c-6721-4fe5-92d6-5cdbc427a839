@use 'shared' as *;

.external-recommendation {
  margin: 0 auto;
  width: min-content;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 24px;

  @include media-breakpoint-down(md) {
    gap: 16px;
  }
  @include media-breakpoint-down(sm) {
    margin: 0;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    width: 100%;
  }

  .full-row {
    grid-column: span 4;

    @include media-breakpoint-down(sm) {
      grid-column: span 2;
    }

    &.desktop {
      @include media-breakpoint-down(sm) {
        display: none;
      }
    }

    &.mobile {
      @include media-breakpoint-up(sm) {
        display: none;
      }
    }
  }

  [mno-article-card] {
    width: 165px;
    @include media-breakpoint-down(sm) {
      width: auto;
    }
  }

  &.foundation {
    width: 100%;

    [mno-article-card] {
      width: 100%;
    }
  }
}
