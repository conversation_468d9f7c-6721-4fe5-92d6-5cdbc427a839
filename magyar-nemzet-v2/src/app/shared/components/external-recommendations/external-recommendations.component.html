<div #externalRecommendationsBlock class="w-full external-recommendation" [ngClass]="{ foundation: isFoundation() }">
  @for (article of externalRecommendations() | slice: 0 : 12; let i = $index; track article.id) {
    <article [data]="article" [styleID]="ArticleCardType.ExternalRecommendation" mno-article-card></article>

    @if (i === 3) {
      <div class="full-row desktop">
        @if (roadblock_ottboxextra(); as ad) {
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        }
      </div>
    }
    @if (i === 5) {
      <div class="full-row mobile">
        @if (mobilrectangle_ottboxextra(); as ad) {
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        }
      </div>
    }
  }
</div>
