<ng-container *ngIf="articleLink">
  <ng-container *ngIf="hasLink; else noLink">
    <a
      *ngIf="(styleID !== ArticleCardType.ExternalRecommendation && !asBrandingBoxEx) || isArray(articleLink); else externalLink"
      [routerLink]="articleLink"
      class="article-link-wrapper"
    >
      <ng-container *ngTemplateOutlet="articleContent"></ng-container>
    </a>
    <ng-template #externalLink>
      <a [href]="articleLink" class="article-link-wrapper" target="_blank">
        <ng-container *ngTemplateOutlet="articleContent"></ng-container>
      </a>
    </ng-template>
  </ng-container>
  <ng-template #noLink>
    <div class="article-link-wrapper">
      <ng-container *ngTemplateOutlet="articleContent"></ng-container>
    </div>
  </ng-template>

  <ng-template #articleContent>
    <ng-container *ngIf="asResult">
      <div class="result-date">
        {{ publishDate | dfnsFormat: 'LLLL d. EEEE p' | titlecase }}
      </div>
    </ng-container>
    <ng-container [ngSwitch]="styleID">
      <ng-container *ngSwitchCase="ArticleCardType.Img16TopTagsTitleLeadBadge">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Img16TopTagsTitleLeadLarge">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Img16TopTagsTitleLeadLargeBorder">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Img4TopTagsTitleLeadSmallBorder">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Img1TopTagsTitleLeadBadge">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Img4TopTitleLeadBadgeLarge">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngIf="asProgram && program">
          <ng-container *ngTemplateOutlet="programDetailsContainer"></ng-container>
        </ng-container>
        <!-- badges in lead -->
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.ImgRightTagsTitleLeadBadge">
        <div class="article-container d-flex align-items-start">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.NoImgBorderLeftTagsTitleLeadBadge">
        <div class="d-flex">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
          </div>
          <!--ng-container *ngTemplateOutlet="img"></ng-container-->
        </div>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.NoImgBorderAllTagsTitleLeadBadge">
        <div class="d-flex">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
          </div>
          <!--ng-container *ngTemplateOutlet="img"></ng-container-->
        </div>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.NoImgBorderBottomRightDateTitleBadge">
        <div class="d-flex">
          <div class="d-flex flex-column flex-1">
            <div class="article-date">
              {{ publishDate | publishDate }}
            </div>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
          </div>
          <!--ng-container *ngTemplateOutlet="img"></ng-container-->
        </div>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.NoImgNoBorderAllTagsTitleLeadBadge">
        <div class="d-flex">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
          </div>
        </div>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.RelatedArticle">
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.NoImgTitleBadge">
        <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.ImgRightTagsTitleBadgeSmall">
        <div class="article-container d-flex align-items-start">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
            <ng-container *ngTemplateOutlet="badges"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
        <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.ImgRightTagsTitleLeadWide">
        <div class="article-container d-flex align-items-start">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
            <ng-container *ngTemplateOutlet="lead"></ng-container>
            <!-- badges in lead -->
            <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
      </ng-container>
      <ng-container *ngSwitchCase="ArticleCardType.ImgRightTagsTitleLeadWideBorder">
        <div class="article-container d-flex align-items-start">
          <div class="d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
            <ng-container *ngTemplateOutlet="lead"></ng-container>
            <!-- badges in lead -->
            <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.DateImgRightTagsTitleLeadWide">
        <div class="article-container d-flex align-items-start justify-content-stretch">
          <div class="article-date">
            <ng-container *ngIf="!thisYear">{{ articleCard.publishYear }}.</ng-container>
            {{ publishDate | dfnsFormat: 'LLLL d. EEEE p' | titlecase }}
          </div>
          <div class="article-content d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
            <ng-container *ngTemplateOutlet="lead"></ng-container>
            <ng-container *ngIf="asProgram && program">
              <ng-container *ngTemplateOutlet="programDetailsContainer"></ng-container>
            </ng-container>
            <!-- badges in lead -->
            <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.DateImgRightTagsTitleLeadWideLarge">
        <div class="article-container d-flex align-items-start justify-content-stretch">
          <div class="article-date">
            <ng-container *ngIf="!thisYear">{{ articleCard.publishYear }}.</ng-container>
            {{ publishDate | dfnsFormat: 'LLLL d. EEEE p' | titlecase }}
          </div>
          <div class="article-content d-flex flex-column flex-1">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
            <ng-container *ngTemplateOutlet="lead"></ng-container>
            <ng-container *ngTemplateOutlet="badges"></ng-container>
            <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.ImgRightTagsTitleLead50">
        <div *ngIf="asProgram && publishDate" class="article-content-counter">
          <ng-container *ngIf="asVideo"
            ><i [size]="styleID === ArticleCardType.Img4TopTitleLeadBadgeLarge ? 22 : 16" color="white" mno-icon="play"></i>
            {{ contentSize }}
            perc
          </ng-container>
          <ng-container *ngIf="asGallery"><i [size]="32" color="white" mno-icon="image"></i> {{ contentSize }} kép </ng-container>
          <ng-container *ngIf="asPodcast"><i [size]="32" color="white" mno-icon="sound-wave-square"></i> {{ contentSize }} perc </ng-container>
          <ng-container *ngIf="asProgram && publishDate">{{ publishDate | dfnsFormat: 'MMMM d. EEEE p' | titlecase }} </ng-container>
        </div>
        <div class="article-container d-flex align-items-stretch">
          <div class="d-flex flex-column">
            <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
            <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
            <ng-container *ngTemplateOutlet="lead"></ng-container>
            <ng-container *ngTemplateOutlet="badges"></ng-container>
            <ng-container *ngTemplateOutlet="opinionAuthor"></ng-container>
            <ng-container *ngIf="asProgram && program">
              <ng-container *ngTemplateOutlet="programDetailsContainer"></ng-container>
            </ng-container>
          </div>
          <ng-container *ngTemplateOutlet="img"></ng-container>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.ExternalRecommendation">
        <ng-container *ngTemplateOutlet="img"></ng-container>
        <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Gallery">
        <ng-container *ngTemplateOutlet="img"></ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleCardType.Podcast">
        <div class="d-flex flex-column">
          <ng-container *ngTemplateOutlet="labelsContainer"></ng-container>
          <ng-container *ngTemplateOutlet="titleContainer"></ng-container>
        </div>
        <ng-container *ngTemplateOutlet="img"></ng-container>
      </ng-container>
    </ng-container>
  </ng-template>
</ng-container>

<ng-template #img>
  <figure *ngIf="showThumbnail" class="article-thumbnail-figure">
    <ng-container *ngIf="dossier && !hasLink; else articleImg">
      <a [routerLink]="articleLink">
        <ng-container *ngTemplateOutlet="articleImg"></ng-container>
      </a>
    </ng-container>
    <ng-template #articleImg>
      @if (asGallery && articleCard?.isAdultsOnly && !isAdultChoice && !isInsideAdultArticleBody) {
        <kesma-adult-overlay>
          <ng-container *ngTemplateOutlet="image"></ng-container>
        </kesma-adult-overlay>
      } @else {
        <ng-container *ngTemplateOutlet="image"></ng-container>
      }
      <ng-template #image>
        <img
          *ngIf="imgRatio === ImgRatio.Ratio169 || !displayed43ThumbnailUrl"
          [alt]="articleCard?.thumbnail?.alt || ''"
          [data]="articleCard?.thumbnailFocusedImages"
          [displayedAspectRatio]="{
            desktop: imgRatio === ImgRatio.Ratio11 ? '1:1' : '16:9',
          }"
          [displayedUrl]="displayedThumbnailUrl"
          class="article-thumbnail"
          [attr.loading]="(imageLazyLoad === undefined && !isPriorityContent) || imageLazyLoad ? 'lazy' : 'eager'"
          withFocusPoint
        />
        <img
          *ngIf="imgRatio !== ImgRatio.Ratio169 && displayed43ThumbnailUrl"
          [alt]="articleCard?.thumbnail?.alt || ''"
          [data]="articleCard?.thumbnailFocusedImages"
          [displayedAspectRatio]="{
            desktop: imgRatio === ImgRatio.Ratio11 ? '1:1' : '4:3',
          }"
          [displayedUrl]="displayed43ThumbnailUrl"
          class="article-thumbnail"
          [attr.loading]="(imageLazyLoad === undefined && !isPriorityContent) || imageLazyLoad ? 'lazy' : 'eager'"
          withFocusPoint
        />
      </ng-template>
    </ng-template>

    <a (click)="$event.stopImmediatePropagation()" *ngIf="sponsorTitle" [href]="sponsorUrl" class="article-sponsor" target="_blank">
      <span>Támogatott tartalom</span><span>{{ sponsorTitle }}</span>
    </a>
    <div *ngIf="styleID === ArticleCardType.Gallery" class="article-content-container">
      <div class="article-title">{{ articleCard?.title }}</div>
      <div *ngIf="articleCard?.articleSource" class="article-lead">Fotó: {{ articleCard?.articleSource }}</div>
    </div>
    <div
      *ngIf="
        (asGallery || asPodcast || asVideo || isLive || (asProgram && publishDate && styleID !== ArticleCardType.DateImgRightTagsTitleLeadWide)) &&
        styleID !== ArticleCardType.ImgRightTagsTitleLead50
      "
      class="article-content-counter"
    >
      <ng-container *ngIf="asVideo"
        ><i [size]="styleID === ArticleCardType.Img4TopTitleLeadBadgeLarge ? 22 : 16" color="white" mno-icon="play"></i> {{ contentSize }} perc
      </ng-container>
      <ng-container *ngIf="asGallery"><i [size]="32" color="white" mno-icon="image"></i> {{ contentSize }} kép </ng-container>
      <ng-container *ngIf="asPodcast"
        ><i [size]="32" color="white" mno-icon="sound-wave-square"></i> {{ contentSize }}
        perc
      </ng-container>
      <ng-container *ngIf="asProgram && styleID !== ArticleCardType.DateImgRightTagsTitleLeadWide && publishDate">
        {{ publishDate | dfnsFormat: 'MMMM d. EEEE p' }}
      </ng-container>
      <ng-container *ngIf="isLive">
        <ng-container *ngIf="isMinuteByMinute; else newsFeedText"> Élő közvetítés</ng-container>
        <ng-template #newsFeedText>
          <i [size]="32" color="white" mno-icon="content-view"></i>
          Élő hírfolyam
        </ng-template>
        | folyamatosan frissül
      </ng-container>
    </div>
  </figure>
</ng-template>

<ng-template #labelsContainer>
  <div class="article-labels">
    <img *ngIf="isOpinion" [size]="labelIconSize" alt="Vélemény ikon" mnoBadge mno-icon type="opinion" />
    <ng-container *ngIf="showArticleLabels">
      <ng-container *ngIf="dossier; else foundationTag">
        <a [routerLink]="['/', 'dosszie', dossier?.slug]" class="article-label">{{ dossier?.title }}</a>
      </ng-container>
      <ng-template #foundationTag>
        <ng-container *ngIf="foundationTagTitle; else normalLabels">
          <span class="article-label">{{ foundationTagTitle }}</span>
        </ng-container>
      </ng-template>
      <ng-template #normalLabels>
        <span *ngFor="let label of labels | slice: 0 : 1" class="article-label">{{ label }}</span>
      </ng-template>
    </ng-container>
  </div>
</ng-template>

<ng-template #titleContainer>
  <ng-container *ngIf="dossier; else articleTitle">
    <a [routerLink]="articleCard?.slug ? articleLink : ['/', 'dosszie', dossier.slug]">
      <ng-container *ngTemplateOutlet="articleTitle"></ng-container>
    </a>
  </ng-container>
  <ng-template #articleTitle>
    <h2 class="article-title">
      {{ articleCard?.title }}
    </h2>
  </ng-template>
</ng-template>

<ng-template #lead>
  <p *ngIf="styleID !== ArticleCardType.ExternalRecommendation && !asGallery && !asPodcast && !asVideo && !dossier" class="article-lead">
    <span class="article-lead-text">{{ articleCard?.excerpt ?? articleCard?.lead }}</span>
    <ng-container
      *ngIf="
        styleID === ArticleCardType.Img4TopTitleLeadBadgeLarge ||
        styleID === ArticleCardType.ImgRightTagsTitleLeadWide ||
        styleID === ArticleCardType.ImgRightTagsTitleLeadWideBorder ||
        styleID === ArticleCardType.DateImgRightTagsTitleLeadWide
      "
    >
      <ng-container *ngTemplateOutlet="badges"></ng-container>
    </ng-container>
  </p>

  <p *ngIf="styleID === ArticleCardType.ExternalRecommendation" class="article-lead">{{ externalRecommendation.title }}</p>

  <p *ngIf="asGallery && articleCard.articleSource" class="article-lead">Fotó: {{ articleCard.articleSource }}</p>
</ng-template>

<ng-template #badges>
  <div *ngIf="!asGallery && !asPodcast && !asVideo && !asProgram" [attr.data-icon-size]="iconSize" class="article-badge-container">
    <img *ngIf="isAdultsOnly" [size]="iconSize" alt="18+ ikon" loading="lazy" mnoBadge mno-icon type="adult" />
    <i *ngIf="isVideoType || hasVideo" [size]="iconSize" mnoBadge mno-icon type="video"></i>
    <i *ngIf="isPodcastType" [size]="iconSize" mnoBadge mno-icon type="podcast"></i>
    <i *ngIf="hasGallery" [size]="iconSize" mnoBadge mno-icon type="gallery"></i>
    <i
      *ngIf="brand === 'mindmegette' && newMindmegetteDesignAllowed"
      [showLabel]="true"
      [size]="iconSize"
      color="color"
      label="Mindmegette"
      mno-icon="mindmegette-logo-mini"
    ></i>
  </div>
</ng-template>

<ng-template #opinionAuthor>
  <mno-opinion-author *ngIf="isOpinion && author" [data]="author"></mno-opinion-author>
</ng-template>

<ng-template #programDetailsContainer>
  <div class="article-program-container">
    <div class="article-program-location"><img [size]="24" alt="Helyszín ikon" mno-icon="location" />{{ program?.locations?.[0]?.title }}</div>
    <div class="article-program-date">
      <img [size]="24" alt="Dátum ikon" mno-icon="clock" />{{ startDate | dfnsFormat: 'MMMM d. EEEE p' }} -
      {{ endDate | dfnsFormat: 'MMMM d. EEEE p' }}
    </div>
  </div>
</ng-template>
