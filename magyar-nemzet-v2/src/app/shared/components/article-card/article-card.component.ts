import { ChangeDetectionStrategy, Component, HostBinding, inject, Input } from '@angular/core';
import {
  AdultOverlayComponent,
  ArticleCard,
  Author,
  backendBoolToBool,
  backendDateToDate,
  BaseComponent,
  buildArticleUrl,
  DossierArticleShort,
  DossierData,
  ExternalRecommendation,
  FocusPointDirective,
  GalleryData,
  MinuteToMinuteState,
  ProgramRecommendationItem,
} from '@trendency/kesma-ui';
import { ArticleCardType, PALCEHOLDER_IMG } from '../../definitions';
import { PublishDatePipe, StorageService, UtilService } from '@trendency/kesma-core';
import { OpinionAuthorComponent } from '../opinion-author/opinion-author.component';
import { BadgeDirective } from '../../directives';
import { IconComponent } from '../icon/icon.component';
import { RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>, <PERSON>I<PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>Case, NgTemplateOutlet, SlicePipe, TitleCasePipe } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';

enum ImgRatio {
  Ratio43 = '43',
  Ratio169 = '169',
  Ratio11 = '11',
}

@Component({
  selector: '[mno-article-card]',
  templateUrl: './article-card.component.html',
  styleUrls: ['./article-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    RouterLink,
    NgTemplateOutlet,
    NgSwitch,
    NgSwitchCase,
    IconComponent,
    FocusPointDirective,
    BadgeDirective,
    NgFor,
    OpinionAuthorComponent,
    SlicePipe,
    TitleCasePipe,
    SlicePipe,
    PublishDatePipe,
    AdultOverlayComponent,
    DateFnsModule,
  ],
})
export class ArticleCardComponent extends BaseComponent<ArticleCard | ExternalRecommendation | DossierArticleShort<Date> | GalleryData> {
  private readonly utils = inject(UtilService);

  @Input() headingLevel: number | 'span' | 'div' = 2;
  startDate?: Date | null;
  endDate?: Date | null;
  @Input() showThumbnail = true;
  @Input() isVideoType = false;
  @Input() hasGallery = false;
  @Input() isAdultsOnly = false;
  @Input() isPodcastType = false;
  @Input()
  @HostBinding('class.is-opinion')
  isOpinion = false;
  @HostBinding('class.img')
  @Input()
  @HostBinding('class.is-interview')
  isInterview = false;
  @Input() isGallery = false;
  @Input() hasVideo = false;
  @Input() hasDossiers = false;
  @HostBinding('class') hostClass = 'article-card';
  @Input() sponsorTitle?: string;
  @Input() sponsorUrl?: string;
  @Input() author?: Author;
  @Input() isSidebar = false;
  @Input() hasLink = true;
  @Input() dossier?: DossierData<Date>;
  @Input() showArticleLabels = true;
  @Input() imageLazyLoad?: boolean;
  imgRatio: ImgRatio = ImgRatio.Ratio169;
  articleLink: string[] | string = [];
  displayedThumbnailUrl?: string;
  displayed43ThumbnailUrl?: string;
  iconSize = 20;
  labelIconSize = 16;
  labels: string[] = [];
  publishDate: Date = new Date();
  thisYear = true;
  contentSize = '0';
  program?: ProgramRecommendationItem;
  foundationTagTitle?: string;
  isAdultChoice = false;
  readonly ArticleCardType = ArticleCardType;
  readonly ImgRatio = ImgRatio;
  newMindmegetteDesignAllowed = false;
  @Input() isInsideAdultArticleBody = false;
  isArray = Array.isArray;
  protected readonly storage = inject(StorageService);
  #asBrandingBoxEx = false;
  #asGallery = false;
  #asPodcast = false;
  #asVideo = false;
  #asProgram = false;
  #asBrandingBox = false;
  #brand = '';
  #inLayout = false;
  #isLive = false;
  #isMinuteByMinute = false;
  #asResult = false;
  #styleID?: ArticleCardType;

  get styleID(): ArticleCardType {
    return this.#styleID as ArticleCardType;
  }

  @Input()
  set styleID(styleID: ArticleCardType) {
    if (styleID === this.#styleID) return;
    this.#styleID = styleID;
    this.setProperties();
    if ([ArticleCardType.Img16TopTagsTitleLeadLarge, ArticleCardType.Img16TopTagsTitleLeadLargeBorder].includes(styleID)) {
      this.iconSize = 24;
    }
  }

  get asGallery(): boolean {
    return this.#asGallery;
  }

  @Input() set asGallery(val: boolean) {
    this.#asGallery = val;
    this.setSize();
    this.setHostClass();
  }

  get asPodcast(): boolean {
    return this.#asPodcast;
  }

  @Input() set asPodcast(val: boolean) {
    this.#asPodcast = val;
    this.setSize();
    this.setHostClass();
  }

  get asVideo(): boolean {
    return this.#asVideo;
  }

  @Input() set asVideo(val: boolean) {
    this.#asVideo = val;
    this.setSize();
    this.setHostClass();
  }

  get asProgram(): boolean {
    return this.#asProgram;
  }

  @Input() set asProgram(value: boolean) {
    this.#asProgram = value;
    this.setSize();
    this.setHostClass();
  }

  get brand(): string {
    return this.#brand;
  }

  @Input() set brand(value: string) {
    this.#brand = value;
    this.#asBrandingBox = true;
    this.setSize();
    this.setHostClass();
  }

  get asBrandingBox(): boolean {
    return this.#asBrandingBox;
  }

  get asBrandingBoxEx(): boolean {
    return this.#asBrandingBoxEx;
  }

  @Input()
  @HostBinding('class.as-branding-box-ex')
  set asBrandingBoxEx(value: boolean) {
    this.#asBrandingBoxEx = value;
    this.setSize();
    this.setHostClass();
  }

  get inLayout(): boolean {
    return this.#inLayout;
  }

  @Input()
  @HostBinding('class.as-branding-box-ex')
  set inLayout(value: boolean) {
    this.#inLayout = value;
    this.setHostClass();
  }

  get isLive(): boolean {
    return this.#isLive;
  }

  @Input()
  @HostBinding('class.is-live')
  set isLive(value: boolean) {
    this.#isLive = value;
  }

  get asResult(): boolean {
    return this.#asResult;
  }

  @Input()
  @HostBinding('class.as-result')
  set asResult(value: boolean) {
    this.#asResult = value;
    this.setProperties();
  }

  get articleCard(): ArticleCard {
    return this.data as ArticleCard;
  }

  get dosserArticle(): DossierArticleShort {
    return this.data as DossierArticleShort;
  }

  get externalRecommendation(): ExternalRecommendation {
    return this.data as ExternalRecommendation;
  }

  get isMinuteByMinute(): boolean {
    return this.#isMinuteByMinute;
  }

  get gallery(): GalleryData {
    return this.data as GalleryData;
  }

  get isPriorityContent(): boolean {
    const isMobile = this.utils.isBrowser() && window.innerWidth < 992;
    return (isMobile && !!this.articleCard.priorityContentMobile) || (!isMobile && !!this.articleCard.priorityContentDesktop);
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.isAdultChoice = this.storage.getSessionStorageData('isAdultChoice') ?? false;
  }

  protected override setProperties(): void {
    if (!this.data) return;

    this.articleLink = this.data
      ? (this.externalRecommendation?.url ?? buildArticleUrl(this.data, this.articleCard?.columnSlug, this.articleCard?.isOpinion, this.articleCard?.brand))
      : [];

    this.displayed43ThumbnailUrl =
      this.externalRecommendation?.imagePath ||
      this.articleCard?.thumbnailUrl43 ||
      this.articleCard?.thumbnail?.url43AspectRatio ||
      this.articleCard?.thumbnail?.url ||
      this.articleCard?.thumbnailUrl ||
      (typeof this.articleCard?.thumbnail === 'string' ? this.articleCard.thumbnail : undefined) ||
      this.articleCard?.highlightedImageUrl ||
      (typeof this.articleCard?.highlightedImage !== 'string' ? this.articleCard.highlightedImage?.url : this.articleCard.highlightedImage) ||
      'assets/images/' + PALCEHOLDER_IMG;
    this.displayedThumbnailUrl =
      this.articleCard?.thumbnailUrl ||
      this.articleCard?.thumbnail?.url ||
      (typeof this.articleCard?.thumbnail === 'string' ? this.articleCard.thumbnail : undefined) ||
      this.articleCard?.highlightedImageUrl ||
      (typeof this.articleCard?.highlightedImage !== 'string' ? this.articleCard.highlightedImage?.url : this.articleCard.highlightedImage) ||
      'assets/images/' + PALCEHOLDER_IMG;

    this.labels =
      ([
        this.articleCard.labelText,
        this.externalRecommendation.siteName,
        this.articleCard.firstTagTitle,
        ...(this.articleCard?.tags ?? []).map(({ title }) => title),
        this.articleCard.columnTitle,
      ].filter((s) => !!s) as string[]) ?? [];

    if (
      [
        ArticleCardType.Img4TopTitleLeadBadgeLarge,
        ArticleCardType.ExternalRecommendation,
        ArticleCardType.DateImgRightTagsTitleLeadWide,
        ArticleCardType.Gallery,
        ArticleCardType.Img4TopTagsTitleLeadSmallBorder,
      ].includes(this.styleID)
    ) {
      this.imgRatio = ImgRatio.Ratio43;
    }
    if (
      [
        ArticleCardType.ImgRightTagsTitleLeadBadge,
        ArticleCardType.ImgRightTagsTitleBadgeSmall,
        ArticleCardType.ImgRightTagsTitleLeadWide,
        ArticleCardType.ImgRightTagsTitleLeadWideBorder,
        ArticleCardType.Img1TopTagsTitleLeadBadge,
      ].includes(this.styleID)
    ) {
      this.imgRatio = ImgRatio.Ratio11;
    }
    if (this.asResult) {
      this.imgRatio = ImgRatio.Ratio169;
    }

    if (this.styleID === ArticleCardType.ImgRightTagsTitleBadgeSmall || this.styleID === ArticleCardType.NoImgTitleBadge) {
      this.iconSize = 16;
      this.labelIconSize = 12;
    }
    this.isOpinion = this.articleCard.isOpinion ?? false;
    this.hasGallery = backendBoolToBool(this.articleCard.hasGallery) ?? false;
    this.isAdultsOnly = backendBoolToBool(this.articleCard.isAdultsOnly) ?? false;
    this.hasDossiers = this.articleCard.hasDossiers ?? false;
    this.isVideoType = backendBoolToBool(this.articleCard.isVideoType) ?? backendBoolToBool(this.articleCard.isVideo) ?? false;
    this.isPodcastType = backendBoolToBool(this.articleCard.isPodcastType) ?? false;
    this.foundationTagTitle = this.articleCard.foundationTagTitle ?? undefined;
    this.#isMinuteByMinute = !!this.articleCard.minuteToMinute && this.articleCard.minuteToMinute !== MinuteToMinuteState.NOT;
    if (!this.articleCard?.author?.slug && this.articleCard?.publicAuthorSlug) {
      this.setData({
        ...this.articleCard,
        author: { ...(this.articleCard.author ?? {}), slug: this.articleCard?.publicAuthorSlug } as Author,
      });
    }
    this.author = this.articleCard.author;
    const publishYear =
      typeof this.articleCard?.publishDate === 'string' ? this.articleCard?.publishDate?.substring(0, 4) : this.articleCard?.publishDate?.getFullYear();

    this.setData({ ...this.data, publishYear } as ArticleCard);
    this.thisYear = (publishYear as number) * 1 === new Date().getFullYear();
    this.publishDate =
      typeof this.articleCard.publishDate === 'string' ? (backendDateToDate(this.articleCard.publishDate) as Date) : (this.articleCard.publishDate as Date);
    if ((this.data as any).program) {
      this.program = (this.data as any).program;
      this.startDate = backendDateToDate(this.program?.startDate as string);
      this.endDate = backendDateToDate(this.program?.endDate as string);
    }

    this.setHostClass();
    this.setSize();
  }

  private setHostClass(): void {
    this.hostClass = [
      'article-card',
      `style-${ArticleCardType[this.styleID]}`,
      this.asVideo ? 'as-video' : undefined,
      this.asGallery ? 'as-gallery' : undefined,
      this.asPodcast ? 'as-podcast' : undefined,
      this.asProgram ? 'as-program' : undefined,
      this.inLayout ? 'in-layout' : undefined,
      this.isLive ? 'is-live' : undefined,
      this.#isMinuteByMinute ? 'is-minute-by-minute' : undefined,
    ]
      .filter((v) => !!v)
      .join(' ');
  }

  private setSize(): void {
    if (!this.data) {
      return;
    }
    if (this.asPodcast) {
      this.contentSize = (this.articleCard.length || this.articleCard.readingTime)?.toString() || '0';
    }
    if (this.asVideo) {
      this.contentSize = (this.articleCard.length || this.articleCard.readingTime)?.toString() || '0';
      this.imgRatio = ImgRatio.Ratio169;
    }
    if (this.asGallery) {
      this.contentSize = (this.gallery?.count || this.articleCard?.length || 0).toString();
      this.articleLink = ['/', 'galeria', this.articleCard?.slug ?? '', this.articleCard?.slug ? '1' : ''];
      this.showThumbnail = true;
    }
    if (this.asProgram) {
      this.articleLink = [
        '/',
        'kulturnemzet',
        this.articleCard?.tags?.[0]?.slug ?? (this.articleCard as ProgramRecommendationItem)?.locations?.[0]?.slug ?? '',
        this.articleCard?.slug ?? '',
      ].filter((s) => s !== '');
      this.showThumbnail = true;
    }
    if (this.asBrandingBoxEx) {
      this.articleLink = this.articleCard.url as string;
    }
  }
}
