@use 'shared' as *;

:host {
  z-index: 1000;
  position: sticky;
  /* use "top" to provide threshold for hitting top of parent */
  top: -1px;
  background-repeat: no-repeat;
  background-position: top center;
  background-color: var(--kui-header-bg-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  --kui-header-bg-color: var(--kui-white);
  --kui-header-fg-color: var(--kui-blue-950);
  --kui-header-link-color: var(--kui-slate-950);
  --kui-header-link-hover-color: var(--kui-blue-700);
  --kui-header-link-button-hover-color: var(--kui-slate-300);

  &.is-dark {
    --kui-header-fg-color: var(--kui-slate-50);
    --kui-header-bg-color: var(--kui-slate-900);
    --kui-header-link-color: var(--kui-white);
    --kui-header-link-hover-color: var(--kui-slate-300);
    --kui-header-link-button-hover-color: var(--kui-slate-50);
  }

  @include media-breakpoint-down(sm) {
    .header-top,
    .header-trending-tag-list,
    .header-bottom {
      max-width: 100vw;
    }
  }

  [mno-breaking-strip] {
    max-width: 100%;
    @include media-breakpoint-down(sm) {
      max-width: 100vw;
    }
  }

  @include media-breakpoint-down(sm) {
    padding: 0 16px;
  }

  &.is-stuck {
    .hidden-on-stuck {
      height: 0;
      min-height: 0;
      opacity: 0;
      padding: 0;
      overflow: hidden;
      @include transition;
    }

    .only-on-stuck {
      height: auto;
      opacity: 1;
      @include transition;
    }

    .header-logo {
      width: 159px;
      height: 44px;
      min-width: 159px;
      min-height: 44px;
      margin-inline: 40px;
      @include transition;
    }

    .header-trending-tag-list {
      gap: 24px;
      width: 50%;
      max-width: unset;
      justify-content: space-around;

      &-item {
        padding: 0;
      }
    }
  }
}

.only-on-stuck {
  height: 0;
  min-height: 0;
  opacity: 0;
  padding: 0;
  overflow: hidden;
  @include transition;
}

.top-wrapper {
  border-bottom: 1px solid var(--kui-header-fg-color);
  width: calc(100% - 168px);
  max-width: $global-wrapper-width;
  @include media-breakpoint-down(sm) {
    width: 100%;
    border-bottom: unset;
  }
}

.header {
  &-top {
    margin: 12px 0 0;
    min-height: 78px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @include media-breakpoint-down(sm) {
      margin: 8px 0 0;
      width: 100%;
      border-bottom: 1px solid var(--kui-header-fg-color);
    }

    &-left {
      display: flex;
      justify-content: flex-start;
    }

    &-right {
      display: flex;
      justify-content: flex-end;
    }

    .header-trending-tag-list.only-on-stuck {
      justify-content: space-around;
    }
  }

  &-bottom {
    margin: 0;
    height: 9px;
    border-top: 4px solid var(--kui-header-fg-color);
    border-bottom: 1px solid var(--kui-header-fg-color);
    width: 100%;
    @include media-breakpoint-down(sm) {
      margin: 0;
      width: 100%;
    }
  }

  &-menu-button {
    padding: 4px;
    cursor: pointer;
    background: var(--kui-header-bg-color);
    border: 1px solid var(--kui-header-bg-color);
    border-radius: 50%;
    margin: 0 12px;
    @include transition;

    @include media-breakpoint-down(sm) {
      margin: 0;
      padding: 0;
    }

    &:hover {
      border: 1px solid var(--kui-header-link-button-hover-color);
      @include transition;
    }

    /*
        &:first-child {
          margin-left: -2px;
        }
        &:last-child {
          margin-right: -2px;
        }
    */
    img {
      width: 24px;
      height: 24px;
      min-width: 24px;
      min-height: 24px;
    }
  }

  &-logo {
    @include transition;
    width: 264px;
    min-width: 264px;
    cursor: pointer;

    @include media-breakpoint-down(sm) {
      width: 168px;
      min-width: 168px;
    }
  }

  &-trending-tag-list {
    display: flex;
    width: calc(100% - 168px);
    max-width: 841px;
    align-items: center;
    min-height: 42px;
    overflow-x: scroll;
    // scrollbar-width: none;
    gap: 28px;

    @include media-breakpoint-down(sm) {
      width: 100%;
      max-width: 100%;
      justify-content: normal;
    }

    &::-webkit-scrollbar {
      display: none;
    }

    &-item {
      font-size: 14px;
      font-weight: 700;
      padding: 4px 10px;
      white-space: nowrap;
      text-transform: capitalize;

      &,
      &:visited,
      &:link,
      &:active {
        color: var(--kui-header-link-color);
      }

      &:hover {
        color: var(--kui-header-link-hover-color);
      }
    }
  }
}

.hamburger-menu {
  position: fixed;
  top: 0;
  right: 0;
  background-color: var(--kui-slate-900);
  background-repeat: no-repeat;
  background-position: top left;
  max-width: 300px;
  scrollbar-width: auto;
  scrollbar-color: var(--slate-50) var(--kui-slate-950);
  overflow-y: scroll;
  height: 100vh;
  padding: 0 8px 22px;
  line-height: 21px;
  gap: 8px;
  display: flex;
  flex-direction: column;
  opacity: 0;
  z-index: -1;
  width: 0;
  overflow-x: hidden;
  @include transition;

  &.open {
    z-index: 1002;
    opacity: 1;
    width: auto;
    @include transition;
  }

  &::-webkit-scrollbar {
    width: auto;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--kui-slate-50);
  }

  &::-webkit-scrollbar-track {
    background-color: var(--kui-slate-950);
  }

  @include media-breakpoint-down(sm) {
    width: 0;
    min-width: 0;
    max-width: 100%;
    &.open {
      width: 100%;
    }
  }

  &-header {
    width: 100%;
    padding: 22px 5px 12px 3px;
    display: flex;
    justify-content: space-between;

    &-logo {
      @include transition;
      width: 164px;
    }

    .close-button {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid transparent;
      border-radius: 50%;
      @include transition;

      img {
        @include transition;
        height: 24px;
        width: 24px;
        margin: 0;
      }

      &:hover {
        img {
          height: 22px;
          width: 22px;
          @include transition;
          margin: 2px;
        }

        border-color: var(--kui-slate-50);
        @include transition;
      }

      cursor: pointer;
    }
  }

  &-search {
    width: 100%;
    background-color: transparent;
    border: 1px solid var(--kui-slate-50);
    border-radius: 2px;
    padding: 8px 12px 9px;
    color: var(--kui-slate-50);

    &::placeholder {
      color: var(--kui-slate-50);
      text-align: center;
      font-size: 16px;
      @include icon('icons/search-white.svg');
      background-position: 33% center;
      background-size: 20px;
      background-repeat: no-repeat;
      margin-left: -20px;
    }

    &[value] {
      background-image: none;
    }
  }

  h2 + ul {
    margin-top: 8px;
  }

  ul {
    gap: 12px;
    display: flex;
    flex-direction: column;

    li {
      .header-menu-item {
        font-weight: 500;
        color: var(--kui-slate-50);
        cursor: pointer;
        display: flex;
        width: 100%;
        justify-content: space-between;
        @include transition;
        padding: 4px 12px 4px 42px;

        &-with-logo {
          display: flex;
          color: var(--kui-slate-50);
          align-items: center;
          gap: 10px;

          img {
            height: 32px;
            width: 32px;
          }
        }

        &.highlighted {
          font-weight: 700;
        }

        &:hover {
          color: var(--kui-blue-500);
        }

        .icon {
          width: 22px;
          height: 22px;
          min-width: 22px;
          min-height: 22px;
          @include transition;
          transform: rotate(0deg);

          &.icon-chevron {
            @include icon('icons/chevron-down-white.svg');
          }

          &.icon-play {
            @include icon('icons/play-white.svg');
          }

          &.icon-image {
            @include icon('icons/image-white.svg');
          }

          &.icon-mic {
            @include icon('icons/mic-white.svg');
          }

          &.icon-mail {
            @include icon('icons/mail-white.svg');
          }

          &.icon-news {
            @include icon('icons/news-white.svg');
          }
        }

        &.active {
          @include GhostButtonHighlight;
        }

        &.active {
          .icon {
            transform: rotate(180deg);
            @include transition;
          }
        }

        &-link {
          font-size: 12px;
          line-height: 16px;
        }
      }

      .header-menu-item-submenu {
        @include transition;

        &:not(.open) {
          height: 0;
          opacity: 0;
          overflow: hidden;
          @include transition;
        }
      }

      h2.header-menu-item,
      .header-menu-item-button {
        color: var(--kui-slate-50);
        border-radius: 2px;
        font-size: 16px;
        line-height: 21px;
        border: 1px solid var(--kui-slate-50);
        padding: 8px 12px;

        &:hover {
          @include GhostButtonHighlight;
        }
      }

      .header-menu-item-button {
        justify-content: center;
        align-items: center;
        gap: 8px;

        .icon {
          width: 20px;
        }
      }

      .header-menu-social {
        &-group {
          border: 1px solid var(--kui-blue-500);
          border-radius: 2px;
          padding: 12px 4px;

          h3 {
            font-size: 14px;
            line-height: 18px;
            font-weight: 700;
            color: var(--kui-slate-50);
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}

.menu-overlay {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--kui-black);
  opacity: 0.48;
  z-index: 1001;

  @include media-breakpoint-down(sm) {
    display: none;
  }
}

.desktop-only {
  @include media-breakpoint-down(md) {
    display: none;
  }
}
button.btn {
  width: 100%;
  font-family: var(--kui-font-primary);
}
