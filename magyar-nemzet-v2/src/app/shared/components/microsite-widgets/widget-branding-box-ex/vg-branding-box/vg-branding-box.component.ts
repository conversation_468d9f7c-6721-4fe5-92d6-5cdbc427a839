import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { format } from 'date-fns';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { ArticleCard } from '@trendency/kesma-ui';
import { environment } from 'src/environments/environment';
import { ArticleCardComponent } from '../../../article-card/article-card.component';
import { IconComponent } from '../../../icon/icon.component';
import { ArticleCardType, SearchArticle, VG_BRANDING_BOX_UTM_PARAMS } from '../../../../definitions';
import { NgForOf } from '@angular/common';
import { VgBrandingBoxService } from '../../../../services';

// ATM branding boxes here only points to articles created on the site copied from the target site
// when we want to switch to really external branding boxes here too, we will have to switch this to `true`
const IS_BRANDING_BOX_REALLY_EXTERNAL = true;

@Component({
  selector: 'app-vg-branding-box',
  templateUrl: './vg-branding-box.component.html',
  styleUrls: ['./vg-branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, IconComponent, NgForOf],
})
export class VgBrandingBoxComponent implements OnInit, OnDestroy {
  public articles: ArticleCard[] = [];
  ArticleCardType = ArticleCardType;

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly vgService: VgBrandingBoxService,
    private readonly changeRef: ChangeDetectorRef
  ) {
    this.bindMethods();
  }

  public ngOnInit(): void {
    this.vgService
      .fetchVgNewestArticles()
      .pipe(takeUntil(this.destroy$))
      .pipe(map((searchArticles) => searchArticles.map(this.mapSearchArticleToBrandingBoxArticle.bind(this))))
      .subscribe((data: ArticleCard[]) => {
        this.articles = data.slice(0, 4);
        this.changeRef.detectChanges();
      });
  }

  public ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private mapSearchArticleToBrandingBoxArticle(article: SearchArticle): ArticleCard {
    let url = null;
    const baseUrl = IS_BRANDING_BOX_REALLY_EXTERNAL ? this.getUrl() : this.getInternalUrl();

    switch (article.contentType) {
      default:
      case 'article':
        url = article.foundationTagSlug
          ? `${baseUrl}/${article.foundationTagSlug}${VG_BRANDING_BOX_UTM_PARAMS}`
          : `${baseUrl}/${article.columnSlug}/${format(article.publishDate, 'yyyy')}/${format(article.publishDate, 'LL')}/${
              article?.slug
            }${VG_BRANDING_BOX_UTM_PARAMS}`;
        break;
      /*
      case 'podcast':
        url = `${baseUrl}/podcastok/${article.slug}`;
        break;
      case 'video':
        url = `${baseUrl}/video/${article.slug}`;
        break;*/
    }

    return {
      ...article,
      url,
      thumbnail: { url: article.thumbnail },
    };
  }

  private getUrl(): string {
    switch (environment.type) {
      case 'prod':
      default:
        return 'https://vg.hu';
      case 'beta':
        return 'http://vgfe.apptest.content.private';
      case 'dev':
      case 'local':
        return 'http://vilaggazdasag.dev.trendency.hu';
    }
  }

  private getInternalUrl(): string {
    return '';
  }

  private bindMethods(): void {
    this.mapSearchArticleToBrandingBoxArticle = this.mapSearchArticleToBrandingBoxArticle.bind(this);
  }
}
