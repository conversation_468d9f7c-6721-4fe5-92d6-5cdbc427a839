<div *ngIf="showTitle" class="result-bar">
  <hr />
  <img *ngIf="isOpinion" [size]="32" alt="Idézőjel ikon" color="darkblue" mno-icon="idezojelek" />
  <h1>{{ title }}</h1>
  <hr />
</div>

<div [class.flex-column]="isMobile" class="top-container d-flex">
  <ng-container *ngFor="let article of articles | slice: 0 : articleCount; let i = index">
    <article
      [asGallery]="isGallery"
      [asPodcast]="isPodcast"
      [asVideo]="isVideo"
      [data]="article"
      [inLayout]="isMobile"
      [isOpinion]="article.isOpinion ?? false"
      [showThumbnail]="isVideo || isPodcast || isGallery || !!article?.thumbnailUrl43 || !!article?.thumbnailUrl || !!article?.thumbnail?.url"
      [styleID]="isMobile && i > 0 && !forceDesktopCardStyle ? mobileStyleID : styleID"
      [imageLazyLoad]="false"
      mno-article-card
    ></article>
  </ng-container>

  <ng-content></ng-content>
</div>
