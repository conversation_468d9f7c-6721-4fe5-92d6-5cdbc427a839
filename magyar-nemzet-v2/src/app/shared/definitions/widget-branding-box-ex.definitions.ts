export type Articles = Readonly<{
  main: BrandingBoxArticle;
  group1: ArticleGroup;
  group2: ArticleGroup;
}>;

export type BrandingBoxArticle = Readonly<{
  url: string;
  thumbnail: string;
  title: string;
  lead: string;
}>;

export type ArticleGroup = [BrandingBoxArticle, BrandingBoxArticle, BrandingBoxArticle];

export type BackendExternalFeedData<C = BackendExternalFeedItem> = {
  title: string;
  description: string;
  copyright: string;
  items: C[];
};

export type BackendExternalFeedItem = {
  title: string;
  imageUrl: string;
  description: string;
  author: string;
  link: string;
};

export type ArticleComponentType = 'main' | 'first-in-group' | 'rest-in-group';

export const VG_BRANDING_BOX_UTM_PARAMS = '?utm_campaign=vg_cikkajanlo&utm_medium=referral&utm_source=magyarnemzet.hu';
