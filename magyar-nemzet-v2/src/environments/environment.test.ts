import { Environment } from '@trendency/kesma-core';

// UAT teszt környezet
export const environment: Environment = {
  production: true,
  type: 'beta',
  apiUrl: 'http://mnofe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '397495361000984',
  siteUrl: 'http://mnofe.apptest.content.private',
  googleClientId: 'AIzaSyDYqfTujUIi9kLZMhxt7y6Uz_h7DyRiWgY',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-WCLP7HF',
  gemiusId: 'nGGQy4hrd6I8io0RcV5gd8TYjwiNGKee.CV0Et2rztv.w7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
